-- KOL管理平台数据库表结构
-- 版本: 1.0
-- 创建时间: 2025-07-21

CREATE EXTENSION IF NOT EXISTS "pg_trgm";
-- 提升文本搜索性能

-- 1. 项目表
CREATE TABLE projects (
    code VARCHAR(50) PRIMARY KEY, -- 项目唯一标识符
    name VARCHAR(255) NOT NULL, -- 项目名称
    description TEXT, -- 项目描述
    email_domain VARCHAR(255), -- 项目邮箱域名
    email_label text[] DEFAULT '{}', -- 项目邮箱标签
    email_sublabel text[] DEFAULT '{}', -- 项目邮箱子标签
    gmail_token_path VARCHAR(255), -- Gmail令牌文件路径
    tracker varchar(50), -- 项目负责人
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() not null, -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() not null -- 更新时间
);

-- 2. 邮件模板表：postmark模板
CREATE TABLE email_templates (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) NOT NULL, -- 模板唯一代码
    name VARCHAR(255) NOT NULL, -- 模板名称
    project_code VARCHAR(50) REFERENCES projects (code) ON DELETE CASCADE, -- 项目编码
    postmark_token VARCHAR(255), -- 邮件服务提供商的令牌
    from_email VARCHAR(255), -- 发件人邮箱
    note TEXT, -- 备注
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() not null, -- 创建时间
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() not null -- 更新时间
        constraint uk_email_templates_code_project_code UNIQUE (code, project_code)
);

-- 3. 爬虫任务表
CREATE TABLE crawler_tasks (
    id SERIAL PRIMARY KEY,
    task_name VARCHAR(255) NOT NULL, -- 任务名称
    source VARCHAR(20) NOT NULL CHECK (
        source IN ('MODASH', 'CREEBLE')
    ), -- 数据源
    platform VARCHAR(20) NOT NULL CHECK (
        platform IN (
            'TIKTOK',
            'INSTAGRAM',
            'YOUTUBE'
        )
    ), -- 爬取平台
    project_code VARCHAR(50) REFERENCES projects (code) ON DELETE CASCADE, -- 项目编码
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (
        status IN (
            'PENDING',
            'RUNNING',
            'COMPLETED',
            'FAILED',
            'CANCELLED'
        )
    ), -- 任务状态
    filters JSONB DEFAULT '{}', -- 过滤条件JSON
    cookies TEXT, -- 爬虫cookie
    log_msg TEXT, -- 日志信息，包含爬取条数crawl_count、有邮箱的条数has_email_count，执行结果信息
    total_duration DECIMAL(10, 2), -- 任务总体耗时（秒）
    task_progress SMALLINT DEFAULT 0 CHECK (
        task_progress >= 0
        AND task_progress <= 100
    ), -- 任务进度（0-100）
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() not null, -- 创建时间
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() not null -- 更新时间
);

-- 4. KOL信息表
CREATE TABLE kols (
    id SERIAL PRIMARY KEY,
    platform VARCHAR(20) NOT NULL CHECK (
        platform IN (
            'TIKTOK',
            'INSTAGRAM',
            'YOUTUBE'
        )
    ), -- KOL所在平台
    social_id VARCHAR(255) NOT NULL, -- KOL的唯一标识符（例如 TikTok 的 link_id）
    nick_name VARCHAR(255) NOT NULL, -- KOL昵称
    project_code VARCHAR(50) REFERENCES projects (code) ON DELETE CASCADE, -- 项目编码
    email VARCHAR(255), -- KOL邮箱
    bio TEXT, -- KOL简介
    followers_count BIGINT, -- 粉丝数
    likes_count BIGINT, -- 点赞数
    source VARCHAR(50), -- KOL来源
    engagement_rate DECIMAL(8, 6), -- 互动率
    mean_views_k DECIMAL(12, 3), -- 平均观看数（千次）
    median_views_k DECIMAL(12, 3), -- 中位观看数（千次）
    tier VARCHAR(20) CHECK (
        tier IN (
            'NANO',
            'MICRO',
            'MID',
            'MACRO',
            'MEGA'
        )
    ), -- KOL分级
    hashtags JSONB DEFAULT '[]', -- 存储hashtag数组
    captions JSONB DEFAULT '[]', -- 存储caption数组
    topics JSONB DEFAULT '[]', -- 存储topic数组
    crawler_task_id INTEGER REFERENCES crawler_tasks (id) ON DELETE SET NULL, -- 爬虫任务ID 出现数据问题时，能够知道是哪个爬虫任务的问题
    note TEXT, -- 备注
    bio_parsed_at TIMESTAMP
    WITH
        TIME ZONE, -- bio解析完成时间
        bio_extracted_email VARCHAR(255), -- 从bio提取的邮箱
        ai_score DECIMAL(5, 2), -- AI评分结果 (0.00-100.00)
        ai_matched BOOLEAN, -- AI是否匹配
        ai_scored_at TIMESTAMP
    WITH
        TIME ZONE, -- AI评分完成时间
        nano_email_fetched_at TIMESTAMP
    WITH
        TIME ZONE, -- nano接口调用完成时间
        nano_extracted_email VARCHAR(255), -- nano获取的邮箱
        email_fetch_status VARCHAR(20) DEFAULT 'PENDING' CHECK (
            email_fetch_status IN (
                'PENDING',
                'BIO_PARSED',
                'AI_SCORED',
                'NANO_FETCHED',
                'COMPLETED'
            )
        ), -- 邮箱获取状态
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() not null, -- 创建时间
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() not null, -- 更新时间
        CONSTRAINT uk_kols_social_id_project_platform UNIQUE (
            social_id,
            project_code,
            platform
        )
);

-- 5. 邮件发送表，记录了所有的邮件发送记录
CREATE TABLE email_send_logs (
    id SERIAL PRIMARY KEY,
    platform VARCHAR(20) CHECK (
        platform IN (
            'TIKTOK',
            'INSTAGRAM',
            'YOUTUBE'
        )
    ), -- 邮件发送的平台
    kol_id INTEGER NOT NULL REFERENCES kols (id) ON DELETE SET NULL, -- KOL ID（外键）
    send_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (
        send_status IN ('PENDING', 'SENT', 'FAILED')
    ), -- 邮件发送状态
    send_date TIMESTAMP
    WITH
        TIME ZONE, -- 邮件发送时间
        project_code VARCHAR(50) REFERENCES projects (code) ON DELETE CASCADE, -- 项目编码
        template_code VARCHAR(50) REFERENCES email_templates (code) ON DELETE CASCADE, -- 邮件模板ID
        from_email VARCHAR(255), -- 发件人邮箱
        to_email VARCHAR(255), -- 收件人邮箱
        note TEXT, -- 备注
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() NOT NULL, -- 创建时间
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() NOT NULL -- 更新时间
);

-- 6. 候选人表：回复了邮件的kol，记录跟进状态
CREATE TABLE candidates (
    id SERIAL PRIMARY KEY,
    platform VARCHAR(20) CHECK (
        platform IS NULL
        OR platform IN (
            'TIKTOK',
            'INSTAGRAM',
            'YOUTUBE'
        )
    ), -- KOL所在平台
    kol_id INTEGER REFERENCES kols (id) ON DELETE CASCADE, -- KOL ID（外键）
    social_id VARCHAR(255), -- KOL的社交媒体ID
    nick_name VARCHAR(255), -- KOL的昵称
    project_code VARCHAR(50) REFERENCES projects (code) ON DELETE CASCADE, -- 项目编码
    reply_email_addr VARCHAR(255), -- 回复邮箱地址
    follow_up_status VARCHAR(50) CHECK (
        follow_up_status IN (
            'PENDING',
            'PROCESSING',
            'DRAFTING',
            'COMPLETED',
            'PAID',
            'NOT_TARGET',
            'OFF'
        )
    ), -- 跟进状态
    follow_up_note TEXT, -- 跟进备注
    first_contact_date TIMESTAMP
    WITH
        TIME ZONE, -- 首次联系时间
        last_contact_date TIMESTAMP
    WITH
        TIME ZONE, -- 最近一次联系时间
        send_round SMALLINT DEFAULT 0 CHECK (
            send_round >= 0
            AND send_round <= 100
        ), -- 发送轮次
        latest_email_send_id INTEGER REFERENCES email_send_logs (id) ON DELETE SET NULL, -- 最新邮件发送ID
        thread_id VARCHAR(255) NOT NULL, -- 关联邮件线程ID
        tracker VARCHAR(50), -- 分配给的跟进人
        note TEXT, -- 备注
        parsed_email JSONB, -- 解析的原始邮箱数据（JSON格式）
        parsed_social_link JSONB, -- 解析的原始社交媒体链接数据（JSON格式）
        need_review BOOLEAN NOT NULL DEFAULT FALSE, -- 是否需要人工审核
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() NOT NULL, -- 创建时间
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() NOT NULL -- 更新时间
);

-- 7. kol单条视频合作绩效表
CREATE TABLE performance (
    id SERIAL PRIMARY KEY,
    platform VARCHAR(20) NOT NULL CHECK (
        platform IN (
            'TIKTOK',
            'INSTAGRAM',
            'YOUTUBE'
        )
    ), -- KOL所在平台
    social_id VARCHAR(255) NOT NULL, -- KOL的社交媒体ID
    project_code NOT NULL VARCHAR(50) REFERENCES projects (code) ON DELETE CASCADE, -- 项目编码
    kol_id INTEGER NOT NULL REFERENCES kols (id) ON DELETE CASCADE, -- KOL ID（外键）
    post_link VARCHAR(500) UNIQUE NOT NULL, -- 帖子链接
    post_date TIMESTAMP
    WITH
        TIME ZONE, -- 发布日期
        views_total BIGINT, -- 总观看数
        likes_total BIGINT, -- 总点赞数
        comments_total BIGINT, -- 总评论数
        shares_total BIGINT, -- 总分享数
        views_day1 BIGINT, -- 第一天观看数
        likes_day1 BIGINT, -- 第一天点赞数
        comments_day1 BIGINT, -- 第一天评论数
        shares_day1 BIGINT, -- 第一天分享数
        views_day3 BIGINT, -- 第三天观看数
        likes_day3 BIGINT, -- 第三天点赞数
        comments_day3 BIGINT, -- 第三天评论数
        shares_day3 BIGINT, -- 第三天分享数
        views_day7 BIGINT, -- 第七天观看数
        likes_day7 BIGINT, -- 第七天点赞数
        comments_day7 BIGINT, -- 第七天评论数
        shares_day7 BIGINT, -- 第七天分享数
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() NOT NULL, -- 创建时间
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() NOT NULL -- 更新时间
);

-- 8. 支付信息表
CREATE TABLE payments (
    id SERIAL PRIMARY KEY,
    performance_id INTEGER NOT NULL REFERENCES performance (id) ON DELETE CASCADE, -- 绩效记录ID（外键）
    payment_amount DECIMAL(10, 2), -- 支付金额
    paypal_accounts VARCHAR(255), -- PayPal账户
    tracker VARCHAR(50), -- 跟进人
    payout_date TIMESTAMP
    WITH
        TIME ZONE, -- 支付日期
        fund_source VARCHAR(100), -- 资金来源
        payment_screenshot_filename VARCHAR(255), -- 支付截图文件名
        note TEXT, -- 备注
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() NOT NULL, -- 创建时间
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW() NOT NULL -- 更新时间
);

-- 数据库注释
COMMENT ON DATABASE postgres IS 'KOL管理平台数据库 - 版本1.0';

-- ========================================
-- 索引创建
-- ========================================

-- KOL表基础索引
CREATE INDEX IF NOT EXISTS idx_kols_project_code ON kols (project_code);

CREATE INDEX IF NOT EXISTS idx_kols_platform ON kols (platform);

CREATE INDEX IF NOT EXISTS idx_kols_email ON kols (email);

CREATE INDEX IF NOT EXISTS idx_kols_followers_count ON kols (followers_count);

CREATE INDEX IF NOT EXISTS idx_kols_tier ON kols (tier);

CREATE INDEX IF NOT EXISTS idx_kols_created_at ON kols (created_at);

-- KOL表邮箱获取流程索引
CREATE INDEX IF NOT EXISTS idx_kols_email_fetch_status ON kols (email_fetch_status);

CREATE INDEX IF NOT EXISTS idx_kols_ai_matched ON kols (ai_matched);

CREATE INDEX IF NOT EXISTS idx_kols_ai_score ON kols (ai_score);

CREATE INDEX IF NOT EXISTS idx_kols_bio_parsed_at ON kols (bio_parsed_at);

CREATE INDEX IF NOT EXISTS idx_kols_ai_scored_at ON kols (ai_scored_at);

CREATE INDEX IF NOT EXISTS idx_kols_nano_email_fetched_at ON kols (nano_email_fetched_at);

-- KOL表复合索引（核心业务查询优化）
CREATE INDEX IF NOT EXISTS idx_kols_project_platform ON kols (project_code, platform);

CREATE INDEX IF NOT EXISTS idx_kols_project_email_status ON kols (
    project_code,
    email_fetch_status
);

CREATE INDEX IF NOT EXISTS idx_kols_project_ai_matched ON kols (project_code, ai_matched);

CREATE INDEX IF NOT EXISTS idx_kols_email_status_email ON kols (email_fetch_status, email);

CREATE INDEX IF NOT EXISTS idx_kols_project_followers ON kols (project_code, followers_count);

CREATE INDEX IF NOT EXISTS idx_kols_project_tier ON kols (project_code, tier);

-- KOL表条件索引（部分索引）
CREATE INDEX IF NOT EXISTS idx_kols_need_email_fetch ON kols (
    email_fetch_status,
    project_code
)
WHERE
    email IS NULL;

CREATE INDEX IF NOT EXISTS idx_kols_ai_matched_true ON kols (project_code, ai_scored_at)
WHERE
    ai_matched = true;

-- KOL表JSONB字段GIN索引（用于高效的多模糊匹配搜索）
CREATE INDEX IF NOT EXISTS idx_kols_hashtags_gin ON kols USING GIN (hashtags);

CREATE INDEX IF NOT EXISTS idx_kols_topics_gin ON kols USING GIN (topics);

CREATE INDEX IF NOT EXISTS idx_kols_captions_gin ON kols USING GIN (captions);

-- 爬虫任务表索引
CREATE INDEX IF NOT EXISTS idx_crawler_tasks_project_code ON crawler_tasks (project_code);

CREATE INDEX IF NOT EXISTS idx_crawler_tasks_status ON crawler_tasks (status);

CREATE INDEX IF NOT EXISTS idx_crawler_tasks_source ON crawler_tasks (source);

CREATE INDEX IF NOT EXISTS idx_crawler_tasks_platform ON crawler_tasks (platform);

CREATE INDEX IF NOT EXISTS idx_crawler_tasks_created_at ON crawler_tasks (created_at);

CREATE INDEX IF NOT EXISTS idx_crawler_tasks_project_status ON crawler_tasks (project_code, status);

-- 邮件发送表索引
CREATE INDEX IF NOT EXISTS idx_email_send_kol_id ON email_send_logs (kol_id);

CREATE INDEX IF NOT EXISTS idx_email_send_project_code ON email_send_logs (project_code);

CREATE INDEX IF NOT EXISTS idx_email_send_status ON email_send_logs (send_status);

CREATE INDEX IF NOT EXISTS idx_email_send_date ON email_send_logs (send_date);

CREATE INDEX IF NOT EXISTS idx_email_send_to_email ON email_send_logs (to_email);

CREATE INDEX IF NOT EXISTS idx_email_send_project_status ON email_send_logs (project_code, send_status);

CREATE INDEX IF NOT EXISTS idx_email_send_kol_date ON email_send_logs (kol_id, send_date);

-- 候选人表索引
CREATE INDEX IF NOT EXISTS idx_candidates_kol_id ON candidates (kol_id);

CREATE INDEX IF NOT EXISTS idx_candidates_project_code ON candidates (project_code);

CREATE INDEX IF NOT EXISTS idx_candidates_follow_up_status ON candidates (follow_up_status);

CREATE INDEX IF NOT EXISTS idx_candidates_tracker ON candidates (tracker);

CREATE INDEX IF NOT EXISTS idx_candidates_reply_email ON candidates (reply_email_addr);

CREATE INDEX IF NOT EXISTS idx_candidates_project_status ON candidates (
    project_code,
    follow_up_status
);

CREATE INDEX IF NOT EXISTS idx_candidates_tracker_status ON candidates (tracker, follow_up_status);

-- Gmail同步相关索引
CREATE INDEX IF NOT EXISTS idx_candidates_need_review ON candidates (need_review);

CREATE INDEX IF NOT EXISTS idx_candidates_project_need_review ON candidates (project_code, need_review);

-- 绩效表索引
CREATE INDEX IF NOT EXISTS idx_performance_kol_id ON performance (kol_id);

CREATE INDEX IF NOT EXISTS idx_performance_project_code ON performance (project_code);

CREATE INDEX IF NOT EXISTS idx_performance_status ON performance (status);

CREATE INDEX IF NOT EXISTS idx_performance_post_date ON performance (post_date);

CREATE INDEX IF NOT EXISTS idx_performance_cpm ON performance (cpm);

CREATE INDEX IF NOT EXISTS idx_performance_project_status ON performance (project_code, status);

CREATE INDEX IF NOT EXISTS idx_performance_kol_date ON performance (kol_id, post_date);

-- 支付表索引
CREATE INDEX IF NOT EXISTS idx_payments_performance_id ON payments (performance_id);

CREATE INDEX IF NOT EXISTS idx_payments_payout_date ON payments (payout_date);

CREATE INDEX IF NOT EXISTS idx_payments_tracker ON payments (tracker);

CREATE INDEX IF NOT EXISTS idx_payments_paypal_accounts ON payments (paypal_accounts);

INSERT INTO
    public.email_templates (
        code,
        "name",
        project_code,
        postmark_token,
        from_email,
        note,
        created_at,
        updated_at
    )
VALUES (
        '********',
        'JP KOL (NOTM105) - Tiktok - 1st reach',
        'TEST_PROJECT',
        '6de357cc-9bbf-45b7-b5a8-32d0c9ea0eaf',
        '<EMAIL>',
        'test',
        '2025-07-22 14:53:47.924',
        '2025-07-22 14:53:47.924'
    );

INSERT INTO
    public.projects (
        code,
        "name",
        description,
        created_at,
        updated_at,
        email_domain,
        email_label,
        email_sublabel,
        gmail_token_path,
        tracker
    )
VALUES (
        'OOG120',
        'TEST',
        'TEST',
        '2025-07-23 18:43:58.231',
        '2025-07-23 18:43:58.231',
        '@7mfitness.com',
        '{Processing,Completed,Drafting,"Not Target❌",Off🤚,Paid,Pending,"TikTok One"}',
        '{"Not Target❌/agency","Not Target❌/mismatch","Off🤚/no response","Off🤚/price too high","Off🤚/turn us down"}',
        '/Users/<USER>/prjs/work/kol/scripts/oog_120_token.pickle',
        'taliya'
    );

-- ========================================
-- KOL表现视图
-- ========================================

-- KOL表现视图：包含KOL基础信息、CPM计算、互动率计算和绩效状态
CREATE VIEW kol_performance_view AS
SELECT
    -- 主键ID字段
    p.id AS performance_id,
    pay.id AS payment_id,
    -- KOL基础信息
    k.platform,
    k.social_id,
    k.nick_name,
    p.project_code,
    p.post_link,
    -- CPM计算：通过payment金额和views_total计算每千次观看成本
    CASE
        WHEN p.views_total > 0 AND pay.payment_amount > 0
        THEN ROUND((pay.payment_amount / (p.views_total / 1000.0))::numeric, 4)
        ELSE NULL
    END AS cpm,
    -- 互动率计算：(点赞数 + 评论数 + 分享数) / 观看数
    CASE
        WHEN p.views_total > 0 AND (p.likes_total IS NOT NULL OR p.comments_total IS NOT NULL OR p.shares_total IS NOT NULL)
        THEN ROUND(
            (
                COALESCE(p.likes_total, 0) +
                COALESCE(p.comments_total, 0) +
                COALESCE(p.shares_total, 0)
            )::numeric / p.views_total::numeric, 6
        )
        ELSE NULL
    END AS engagement_rate,
    -- 绩效状态评估：基于CPM值进行判断
    CASE
        WHEN p.views_total IS NULL OR p.post_date IS NULL THEN 'FAILED'
        WHEN pay.payment_amount IS NULL OR p.views_total <= 0 THEN 'PENDING'
        WHEN (pay.payment_amount / (p.views_total / 1000.0)) > 50 THEN 'POOR'
        WHEN (pay.payment_amount / (p.views_total / 1000.0)) > 20 AND (pay.payment_amount / (p.views_total / 1000.0)) <= 50 THEN 'AVERAGE'
        WHEN (pay.payment_amount / (p.views_total / 1000.0)) > 5 AND (pay.payment_amount / (p.views_total / 1000.0)) <= 20 THEN 'GOOD'
        WHEN (pay.payment_amount / (p.views_total / 1000.0)) <= 5 THEN 'EXCELLENT'
        ELSE 'PENDING'
    END AS status,
    p.views_total,
    p.likes_total,
    p.comments_total,
    p.shares_total,
    p.post_date,
    pay.payment_amount,
    p.created_at,
    p.updated_at
FROM
    performance p
    INNER JOIN kols k ON p.kol_id = k.id
    LEFT JOIN payments pay ON p.id = pay.performance_id
ORDER BY
    p.created_at DESC;

-- 为视图添加注释
COMMENT ON VIEW kol_performance_view IS 'KOL表现视图 - 包含KOL基础信息、CPM计算、互动率计算和绩效状态评估';