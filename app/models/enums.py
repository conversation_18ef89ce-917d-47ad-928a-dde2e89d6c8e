"""
数据库枚举类型定义
"""

from enum import Enum


class PlatformEnum(str, Enum):
    """社交媒体平台枚举"""

    TIKTOK = "TIKTOK"
    INSTAGRAM = "INSTAGRAM"
    YOUTUBE = "YOUTUBE"


class SourceEnum(str, Enum):
    """数据源枚举"""

    MODASH = "MODASH"
    CREEBLE = "CREEBLE"


class CrawlerTaskStatusEnum(str, Enum):
    """爬虫任务状态枚举"""

    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class EmailSendStatusEnum(str, Enum):
    """邮件发送状态枚举"""

    PENDING = "PENDING"
    SENT = "SENT"
    FAILED = "FAILED"


class FollowUpStatusEnum(str, Enum):
    """候选人跟进状态枚举"""

    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    DRAFTING = "DRAFTING"
    COMPLETED = "COMPLETED"
    PAID = "PAID"
    NOT_TARGET = "NOT_TARGET"
    OFF = "OFF"


class PerformanceStatusEnum(str, Enum):
    """绩效状态枚举"""

    PENDING = "PENDING"
    POOR = "POOR"
    AVERAGE = "AVERAGE"
    GOOD = "GOOD"
    EXCELLENT = "EXCELLENT"
    FAILED = "FAILED"


class KOLTierEnum(str, Enum):
    """KOL分级枚举"""

    NANO = "NANO"
    MICRO = "MICRO"
    MID = "MID"
    MACRO = "MACRO"
    MEGA = "MEGA"


class EmailFetchStatusEnum(str, Enum):
    """邮箱获取状态枚举"""

    PENDING = "PENDING"
    BIO_PARSED = "BIO_PARSED"
    AI_SCORED = "AI_SCORED"
    NANO_FETCHED = "NANO_FETCHED"
    COMPLETED = "COMPLETED"


class KOLActivityStatusEnum(str, Enum):
    """KOL活动状态枚举"""

    INBOUND = "INBOUND"  # 入库（爬取到了）
    EMAIL_ACQUIRED = "EMAIL_ACQUIRED"  # 获取邮件
    CANDIDATE = "CANDIDATE"  # 候选人
    PAID = "PAID"  # 已合作
    ENDED = "ENDED"  # 合作结束
