"""
KOL信息表模型
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from sqlalchemy import (
    DECIMAL,
    BigInteger,
    DateTime,
    Enum,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel
from app.models.enums import EmailFetchStatusEnum, KOLTierEnum, PlatformEnum


class KOL(BaseModel):
    """KOL信息表模型"""

    __tablename__ = "kols"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="主键ID"
    )

    platform: Mapped[PlatformEnum] = mapped_column(
        Enum(PlatformEnum), nullable=False, comment="KOL所在平台"
    )

    social_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="KOL的社交媒体标识符（例如 TikTok 的 link_id）",
    )

    nick_name: Mapped[str] = mapped_column(
        String(255), nullable=False, comment="KOL昵称"
    )

    project_code: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("projects.code", ondelete="CASCADE"),
        nullable=False,
        comment="项目编码",
    )

    email: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, comment="KOL邮箱"
    )

    bio: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="KOL简介")

    followers_count: Mapped[Optional[int]] = mapped_column(
        BigInteger, nullable=True, comment="粉丝数"
    )

    likes_count: Mapped[Optional[int]] = mapped_column(
        BigInteger, nullable=True, comment="点赞数"
    )

    source: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True, comment="KOL来源"
    )

    engagement_rate: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(8, 6), nullable=True, comment="互动率"
    )

    mean_views_k: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(12, 3), nullable=True, comment="平均观看数（千次）"
    )

    median_views_k: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(12, 3), nullable=True, comment="中位观看数（千次）"
    )

    tier: Mapped[Optional[KOLTierEnum]] = mapped_column(
        Enum(KOLTierEnum), nullable=True, comment="KOL分级"
    )

    hashtags: Mapped[Optional[List[str]]] = mapped_column(
        JSONB, default=[], nullable=True, comment="存储hashtag数组"
    )

    captions: Mapped[Optional[List[str]]] = mapped_column(
        JSONB, default=[], nullable=True, comment="存储caption数组"
    )

    topics: Mapped[Optional[List[str]]] = mapped_column(
        JSONB, default=[], nullable=True, comment="存储topic数组"
    )

    crawler_task_id: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("crawler_tasks.id", ondelete="SET NULL"),
        nullable=True,
        comment="爬虫任务ID 出现数据问题时，能够知道是哪个爬虫任务的问题",
    )

    note: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="备注")

    # Email获取状态相关字段
    bio_parsed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, comment="bio解析完成时间"
    )

    bio_extracted_email: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, comment="从bio提取的邮箱"
    )

    ai_score: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(5, 2), nullable=True, comment="AI评分结果 (0.00-100.00)"
    )

    ai_matched: Mapped[Optional[bool]] = mapped_column(
        nullable=True, comment="AI是否匹配"
    )

    ai_scored_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, comment="AI评分完成时间"
    )

    nano_email_fetched_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, comment="nano接口调用完成时间"
    )

    nano_extracted_email: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, comment="nano获取的邮箱"
    )

    email_fetch_status: Mapped[Optional[EmailFetchStatusEnum]] = mapped_column(
        Enum(EmailFetchStatusEnum),
        nullable=True,
        default=EmailFetchStatusEnum.PENDING,
        comment="邮箱获取状态",
    )

    # 关联关系
    project: Mapped["Project"] = relationship("Project", back_populates="kols")

    crawler_task: Mapped[Optional["CrawlerTask"]] = relationship(
        "CrawlerTask", back_populates="kols"
    )

    email_send_logs: Mapped[List["EmailSend"]] = relationship(
        "EmailSend", back_populates="kol"
    )

    performances: Mapped[List["Performance"]] = relationship(
        "Performance", back_populates="kol", cascade="all, delete-orphan"
    )

    # 表级约束
    __table_args__ = (
        UniqueConstraint(
            "social_id",
            "project_code",
            "platform",
            name="uk_kols_social_id_project_platform",
        ),
    )

    def __repr__(self) -> str:
        return f"<KOL(id={self.id}, social_id='{self.social_id}', nick_name='{self.nick_name}')>"
