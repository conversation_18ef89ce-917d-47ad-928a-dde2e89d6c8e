"""
支付信息表模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import DECIMAL, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class Payment(BaseModel):
    """支付信息表模型"""

    __tablename__ = "payments"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="主键ID"
    )

    performance_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("performance.id", ondelete="CASCADE"),
        nullable=False,
        comment="绩效记录ID（外键）",
    )

    payment_amount: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(10, 2), nullable=True, comment="支付金额"
    )

    paypal_accounts: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, comment="PayPal账户"
    )

    tracker: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True, comment="跟进人"
    )

    payout_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, comment="支付日期"
    )

    fund_source: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, comment="资金来源"
    )

    payment_screenshot_filename: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, comment="支付截图文件名"
    )

    note: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="备注")

    # 关联关系
    performance: Mapped["Performance"] = relationship(
        "Performance", back_populates="payments"
    )

    def __repr__(self) -> str:
        return f"<Payment(id={self.id}, performance_id={self.performance_id}, amount={self.payment_amount})>"
