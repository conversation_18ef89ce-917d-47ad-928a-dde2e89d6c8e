"""
数据库基础配置和通用模型基类
"""

from datetime import datetime

from sqlalchemy import DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Mapped, mapped_column

# 创建基础模型类
Base = declarative_base()


class TimestampMixin:
    """时间戳混入类，为模型添加创建时间和更新时间字段"""

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间",
    )


class BaseModel(Base, TimestampMixin):
    """基础模型类，包含时间戳字段"""

    __abstract__ = True
