from typing import Any, Dict, Generic, List, Optional, Tuple, Type, TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select, tuple_
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.models.base import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD基类，实现默认的CRUD操作
        """
        self.model = model

    # 同步方法
    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """通过ID获取对象"""
        return db.query(self.model).filter(self.model.id == id).first()

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """获取多个对象"""
        return (
            db.query(self.model)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """创建新对象"""
        obj_in_data = jsonable_encoder(obj_in)

        # 处理空字符串字段，将其转换为 None
        for key, value in obj_in_data.items():
            if value == "":
                obj_in_data[key] = None

        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def create_without_commit(
        self, db: Session, *, obj_in: CreateSchemaType
    ) -> ModelType:
        """创建新对象但不提交事务"""
        obj_in_data = jsonable_encoder(obj_in)

        # 处理空字符串字段，将其转换为 None
        for key, value in obj_in_data.items():
            if value == "":
                obj_in_data[key] = None

        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.flush()  # 刷新以获取ID，但不提交
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        """更新对象"""
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        # 处理空字符串字段，将其转换为 None
        for key, value in update_data.items():
            if value == "":
                update_data[key] = None

        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: Any) -> ModelType:
        """删除对象"""
        obj = db.query(self.model).get(id)
        db.delete(obj)
        db.commit()
        return obj

    def remove_without_commit(self, db: Session, *, id: Any) -> ModelType:
        """删除对象但不提交事务"""
        obj = db.query(self.model).get(id)
        if obj is None:
            raise ValueError(f"No {self.model.__name__} found with id: {id}")
        db.delete(obj)
        db.flush()  # 刷新以确保删除操作被执行，但不提交
        return obj

    # 异步方法
    async def async_get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
        """异步通过ID获取对象"""
        result = await db.execute(select(self.model).filter(self.model.id == id))
        return result.scalars().first()

    async def async_get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """异步获取多个对象"""
        result = await db.execute(
            select(self.model)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def async_create(
        self, db: AsyncSession, *, obj_in: CreateSchemaType
    ) -> ModelType:
        """异步创建新对象"""
        obj_in_data = jsonable_encoder(obj_in)

        # 处理空字符串字段，将其转换为 None
        for key, value in obj_in_data.items():
            if value == "":
                obj_in_data[key] = None

        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def async_update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        """异步更新对象"""
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        # 处理空字符串字段，将其转换为 None
        for key, value in update_data.items():
            if value == "":
                update_data[key] = None

        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def async_remove(self, db: AsyncSession, *, id: Any) -> ModelType:
        """异步删除对象"""
        obj = await self.async_get(db, id=id)
        await db.delete(obj)
        await db.commit()
        return obj

    async def async_upsert(
        self,
        db: AsyncSession,
        *,
        obj_in: Union[CreateSchemaType, Dict[str, Any]],
        conflict_columns: List[str],
    ) -> ModelType:
        """
        异步 upsert 操作（插入或更新）

        Args:
            db: 异步数据库会话
            obj_in: 要插入或更新的数据
            conflict_columns: 冲突检测的列名列表

        Returns:
            创建或更新后的对象
        """
        if isinstance(obj_in, dict):
            obj_data = obj_in
        else:
            obj_data = jsonable_encoder(obj_in)

        # 处理空字符串字段，将其转换为 None
        for key, value in obj_data.items():
            if value == "":
                obj_data[key] = None

        # 使用 PostgreSQL 的 ON CONFLICT 语法
        stmt = insert(self.model).values(**obj_data)

        # 设置冲突时的更新操作
        update_dict = {
            key: stmt.excluded[key]
            for key in obj_data.keys()
            if key not in conflict_columns  # 排除冲突检测列
        }

        if update_dict:
            stmt = stmt.on_conflict_do_update(
                index_elements=conflict_columns, set_=update_dict
            )
        else:
            stmt = stmt.on_conflict_do_nothing(index_elements=conflict_columns)

        # 返回插入或更新的记录
        stmt = stmt.returning(self.model)

        result = await db.execute(stmt)
        await db.commit()

        return result.scalar_one()

    async def async_batch_upsert(
        self,
        db: AsyncSession,
        *,
        objs_in: List[Union[CreateSchemaType, Dict[str, Any]]],
        conflict_columns: List[str],
        batch_size: int = 1000,
    ) -> List[ModelType]:
        """
        异步批量 upsert 操作

        Args:
            db: 异步数据库会话
            objs_in: 要插入或更新的数据列表
            conflict_columns: 冲突检测的列名列表
            batch_size: 批处理大小

        Returns:
            创建或更新后的对象列表
        """
        if not objs_in:
            return []

        results = []

        # 分批处理
        for i in range(0, len(objs_in), batch_size):
            batch = objs_in[i : i + batch_size]

            # 准备批量数据
            batch_data = []
            for obj_in in batch:
                if isinstance(obj_in, dict):
                    obj_data = obj_in
                else:
                    obj_data = jsonable_encoder(obj_in)

                # 处理空字符串字段
                for key, value in obj_data.items():
                    if value == "":
                        obj_data[key] = None

                batch_data.append(obj_data)

            # 执行批量 upsert
            stmt = insert(self.model).values(batch_data)

            # 设置冲突时的更新操作
            if batch_data:
                update_dict = {
                    key: stmt.excluded[key]
                    for key in batch_data[0].keys()
                    if key not in conflict_columns
                }

                if update_dict:
                    stmt = stmt.on_conflict_do_update(
                        index_elements=conflict_columns, set_=update_dict
                    )
                else:
                    stmt = stmt.on_conflict_do_nothing(index_elements=conflict_columns)

            # 返回插入或更新的记录
            stmt = stmt.returning(self.model)

            result = await db.execute(stmt)
            batch_results = result.scalars().all()
            results.extend(batch_results)

        await db.commit()
        return results

    def get_multi_by_filter(
        self,
        db: Session,
        *,
        filter_tuples: List[Tuple[str, str, str]],
        filter_columns: List[str] = ["social_id", "platform", "project_code"],
    ) -> Dict[Tuple[str, str, str], ModelType]:
        """
        根据多个过滤条件批量查询记录，使用高效的IN查询

        Args:
            db: 数据库会话
            filter_tuples: 过滤条件元组列表，例如 [("user1", "INSTAGRAM", "PROJECT1"), ...]
            filter_columns: 对应的列名列表，默认为 ["social_id", "platform", "project_code"]

        Returns:
            Dict: 以过滤条件元组为key，模型对象为value的字典
        """
        if not filter_tuples or not filter_columns:
            return {}

        if len(filter_columns) != 3:
            raise ValueError("filter_columns必须包含3个列名")

        # 获取模型的列属性
        try:
            col1 = getattr(self.model, filter_columns[0])
            col2 = getattr(self.model, filter_columns[1])
            col3 = getattr(self.model, filter_columns[2])
        except AttributeError as e:
            raise ValueError(f"模型 {self.model.__name__} 不包含列: {e}")

        # 构建查询条件：使用tuple_和IN查询实现高效批量查询
        # 这比多个OR条件更高效，特别是当数据量大时
        query = db.query(self.model).filter(tuple_(col1, col2, col3).in_(filter_tuples))

        # 执行查询
        results = query.all()

        # 构建结果字典，key为过滤条件元组，value为模型对象
        result_dict = {}
        for obj in results:
            # 提取对象的对应字段值作为key
            key = (
                str(getattr(obj, filter_columns[0])),
                str(getattr(obj, filter_columns[1])).split("PlatformEnum.")[1],
                str(getattr(obj, filter_columns[2])),
            )
            result_dict[key] = obj

        return result_dict
