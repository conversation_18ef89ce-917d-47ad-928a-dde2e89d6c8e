"""
绩效和支付统一管理的CRUD操作
"""

from typing import Optional, Tu<PERSON>

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.crud import payment, performance
from app.models.kol import KOL
from app.models.payment import Payment
from app.models.performance import Performance
from app.schemas.payment import PaymentCreateInternal
from app.schemas.performance import PerformanceCreate
from app.schemas.performance_payment import (
    PerformancePaymentCreate,
    PerformancePaymentResponse,
)
from app.utils.file_handler import FileHandler


class CRUDPerformancePayment:
    """绩效和支付统一管理的CRUD操作类"""

    def find_kol_by_business_fields(
        self, db: Session, *, platform, social_id: str, project_code: str
    ) -> Optional[KOL]:
        """根据业务字段查找KOL"""
        return (
            db.query(KOL)
            .filter(
                KOL.platform == platform,
                KOL.social_id == social_id,
                KOL.project_code == project_code,
            )
            .first()
        )

    def create_performance_payment(
        self,
        db: Session,
        *,
        obj_in: PerformancePaymentCreate,
        payment_screenshot_filename: Optional[str] = None,
    ) -> Tuple[Performance, Payment]:
        """
        统一创建绩效和支付记录，使用数据库事务保证数据一致性

        Args:
            db: 数据库会话
            obj_in: 创建数据
            payment_screenshot_filename: 支付截图文件名（可选）

        Returns:
            (绩效记录, 支付记录) 元组

        Raises:
            ValueError: 当找不到KOL或数据验证失败时
            IntegrityError: 当数据库约束违反时
        """
        try:
            # 1. 根据业务字段查找KOL
            kol = self.find_kol_by_business_fields(
                db,
                platform=obj_in.platform,
                social_id=obj_in.social_id,
                project_code=obj_in.project_code,
            )
            if not kol:
                raise ValueError(
                    f"找不到匹配的KOL: platform={obj_in.platform.value}, "
                    f"social_id={obj_in.social_id}, project_code={obj_in.project_code}"
                )

            # 2. 创建绩效记录
            performance_data = PerformanceCreate(
                platform=obj_in.platform,
                social_id=obj_in.social_id,
                kol_id=kol.id,  # 使用找到的kol_id
                project_code=obj_in.project_code,
                post_link=obj_in.post_link,
                post_date=obj_in.post_date,
            )

            created_performance = performance.create_performance(
                db, obj_in=performance_data
            )

            # 3. 创建支付记录
            payment_data = PaymentCreateInternal(
                performance_id=created_performance.id,
                payment_amount=obj_in.payment_amount,
                paypal_accounts=obj_in.paypal_accounts,
                tracker=obj_in.tracker,
                payout_date=obj_in.payout_date,
                fund_source=obj_in.fund_source,
                payment_screenshot_filename=payment_screenshot_filename,
                note=obj_in.note,
            )

            created_payment = payment.create_internal(db, obj_in=payment_data)

            # 提交事务
            db.commit()

            return created_performance, created_payment

        except IntegrityError as e:
            db.rollback()
            if "post_link" in str(e):
                raise ValueError(f"帖子链接 '{obj_in.post_link}' 已存在")
            else:
                raise ValueError(f"数据库约束违反: {str(e)}")
        except Exception as e:
            db.rollback()
            raise e

    def convert_to_response(
        self, performance_obj: Performance, payment_obj: Payment
    ) -> PerformancePaymentResponse:
        """将数据库对象转换为响应Schema"""
        # 获取完整的文件路径
        screenshot_path = None
        if payment_obj.payment_screenshot_filename:
            screenshot_path = FileHandler.get_payment_screenshot_path(
                payment_obj.payment_screenshot_filename
            )

        return PerformancePaymentResponse(
            # 绩效信息
            performance_id=performance_obj.id,
            platform=performance_obj.platform,
            social_id=performance_obj.social_id,
            kol_id=performance_obj.kol_id,
            project_code=performance_obj.project_code,
            post_link=performance_obj.post_link,
            post_date=performance_obj.post_date,
            performance_created_at=performance_obj.created_at,
            # 支付信息
            payment_id=payment_obj.id,
            payment_amount=payment_obj.payment_amount,
            paypal_accounts=payment_obj.paypal_accounts,
            tracker=payment_obj.tracker,
            payout_date=payment_obj.payout_date,
            fund_source=payment_obj.fund_source,
            payment_screenshot_filename=payment_obj.payment_screenshot_filename,
            payment_screenshot_path=screenshot_path,
            note=payment_obj.note,
            payment_created_at=payment_obj.created_at,
        )


# 创建CRUD实例
performance_payment = CRUDPerformancePayment()
