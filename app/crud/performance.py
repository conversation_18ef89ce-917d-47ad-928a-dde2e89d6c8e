"""
绩效CRUD操作
"""

from typing import List, Optional

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.enums import PlatformEnum
from app.models.kol import KOL
from app.models.performance import Performance
from app.schemas.performance import PerformanceCreate, PerformanceUpdate


class CRUDPerformance(CRUDBase[Performance, PerformanceCreate, PerformanceUpdate]):
    """绩效CRUD操作类"""

    def get_by_post_link(self, db: Session, *, post_link: str) -> Optional[Performance]:
        """根据帖子链接获取绩效记录"""
        return db.query(Performance).filter(Performance.post_link == post_link).first()

    def find_kol_by_business_fields(
        self, db: Session, *, platform: PlatformEnum, social_id: str, project_code: str
    ) -> Optional[KOL]:
        """根据业务字段查找KOL"""
        return (
            db.query(KOL)
            .filter(
                KOL.platform == platform,
                KOL.social_id == social_id,
                KOL.project_code == project_code,
            )
            .first()
        )

    def get_by_kol(
        self, db: Session, *, kol_id: int, skip: int = 0, limit: int = 100
    ) -> List[Performance]:
        """根据KOL ID获取绩效记录 - 使用idx_performance_kol_date复合索引"""
        return (
            db.query(Performance)
            .filter(Performance.kol_id == kol_id)
            .order_by(Performance.post_date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_project(
        self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[Performance]:
        """根据项目代码获取绩效记录 - 使用idx_performance_project_code索引"""
        return (
            db.query(Performance)
            .filter(Performance.project_code == project_code)
            .order_by(Performance.post_date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_platform(
        self,
        db: Session,
        *,
        platform: PlatformEnum,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Performance]:
        """根据平台获取绩效记录"""
        query = db.query(Performance).filter(Performance.platform == platform)

        if project_code:
            query = query.filter(Performance.project_code == project_code)

        return (
            query.order_by(Performance.post_date.desc()).offset(skip).limit(limit).all()
        )

    def get_top_performers(
        self,
        db: Session,
        *,
        project_code: Optional[str] = None,
        platform: Optional[PlatformEnum] = None,
        limit: int = 10,
    ) -> List[Performance]:
        """获取表现最佳的绩效记录（按观看数排序）"""
        query = db.query(Performance).filter(Performance.views_total.isnot(None))

        if project_code:
            query = query.filter(Performance.project_code == project_code)

        if platform:
            query = query.filter(Performance.platform == platform)

        return query.order_by(Performance.views_total.desc()).limit(limit).all()

    def search_performances(
        self,
        db: Session,
        *,
        kol_id: Optional[int] = None,
        platform: Optional[PlatformEnum] = None,
        project_code: Optional[str] = None,
        min_views: Optional[int] = None,
        max_views: Optional[int] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Performance]:
        """搜索绩效记录"""
        query = db.query(Performance)

        if kol_id:
            query = query.filter(Performance.kol_id == kol_id)

        if platform:
            query = query.filter(Performance.platform == platform)

        if project_code:
            query = query.filter(Performance.project_code == project_code)

        if min_views is not None:
            query = query.filter(Performance.views_total >= min_views)

        if max_views is not None:
            query = query.filter(Performance.views_total <= max_views)

        return (
            query.order_by(Performance.post_date.desc()).offset(skip).limit(limit).all()
        )

    def get_performance_statistics(
        self, db: Session, *, project_code: Optional[str] = None
    ) -> dict:
        """获取绩效统计信息"""
        query = db.query(Performance)

        if project_code:
            query = query.filter(Performance.project_code == project_code)

        total = query.count()

        # 按平台统计
        platform_stats = {}
        for platform in PlatformEnum:
            count = query.filter(Performance.platform == platform).count()
            platform_stats[platform.value] = count

        return {"total": total, "platform_stats": platform_stats}

    def create_performance(
        self, db: Session, *, obj_in: PerformanceCreate
    ) -> Performance:
        """创建绩效记录，支持自动查找KOL，检查帖子链接唯一性"""
        # 检查帖子链接是否已存在
        existing = self.get_by_post_link(db, post_link=obj_in.post_link)
        if existing:
            raise ValueError(f"帖子链接 '{obj_in.post_link}' 已存在")

        # 如果没有提供kol_id，根据业务字段查找
        if obj_in.kol_id is None:
            kol = self.find_kol_by_business_fields(
                db,
                platform=obj_in.platform,
                social_id=obj_in.social_id,
                project_code=obj_in.project_code,
            )
            if not kol:
                raise ValueError(
                    f"找不到匹配的KOL: platform={obj_in.platform.value}, "
                    f"social_id={obj_in.social_id}, project_code={obj_in.project_code}"
                )
            # 设置找到的kol_id
            obj_in.kol_id = kol.id

        return self.create(db, obj_in=obj_in)


# 创建CRUD实例
performance = CRUDPerformance(Performance)
