"""
邮件发送CRUD操作
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.email_send_logs import EmailSend
from app.models.enums import EmailSendStatusEnum, PlatformEnum
from app.schemas.email_send_logs import (
    EmailSendCreate,
    EmailSendCreateInternal,
    EmailSendUpdate,
)


class CRUDEmailSend(CRUDBase[EmailSend, EmailSendCreate, EmailSendUpdate]):
    """邮件发送CRUD操作类"""

    def create_internal(
        self, db: Session, *, obj_in: EmailSendCreateInternal
    ) -> EmailSend:
        """使用内部schema创建邮件发送记录"""
        return self.create(db, obj_in=obj_in)

    def get_by_kol(
        self, db: Session, *, kol_id: int, skip: int = 0, limit: int = 100
    ) -> List[EmailSend]:
        """根据KOL ID获取邮件发送记录 - 使用idx_email_send_kol_date复合索引"""
        return (
            db.query(EmailSend)
            .filter(EmailSend.kol_id == kol_id)
            .order_by(
                EmailSend.send_date.desc()
            )  # 优化：使用send_date排序以利用复合索引
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_project(
        self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[EmailSend]:
        """根据项目代码获取邮件发送记录 - 使用idx_email_send_project_code索引"""
        return (
            db.query(EmailSend)
            .filter(EmailSend.project_code == project_code)
            .order_by(EmailSend.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_status(
        self,
        db: Session,
        *,
        status: EmailSendStatusEnum,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[EmailSend]:
        """根据发送状态获取邮件记录"""
        query = db.query(EmailSend).filter(EmailSend.send_status == status)

        if project_code:
            query = query.filter(EmailSend.project_code == project_code)

        return (
            query.order_by(EmailSend.created_at.desc()).offset(skip).limit(limit).all()
        )

    def get_by_template(
        self, db: Session, *, template_code: str, skip: int = 0, limit: int = 100
    ) -> List[EmailSend]:
        """根据邮件模板获取发送记录"""
        return (
            db.query(EmailSend)
            .filter(EmailSend.template_code == template_code)
            .order_by(EmailSend.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_pending_emails(self, db: Session, *, limit: int = 100) -> List[EmailSend]:
        """获取待发送的邮件"""
        return (
            db.query(EmailSend)
            .filter(EmailSend.send_status == EmailSendStatusEnum.PENDING)
            .order_by(EmailSend.created_at.asc())
            .limit(limit)
            .all()
        )

    def get_by_date_range(
        self,
        db: Session,
        *,
        start_date: datetime,
        end_date: datetime,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[EmailSend]:
        """根据日期范围获取邮件发送记录"""
        query = db.query(EmailSend).filter(
            EmailSend.send_date >= start_date, EmailSend.send_date <= end_date
        )

        if project_code:
            query = query.filter(EmailSend.project_code == project_code)

        return (
            query.order_by(EmailSend.send_date.desc()).offset(skip).limit(limit).all()
        )

    def update_send_status(
        self,
        db: Session,
        *,
        email_id: int,
        status: EmailSendStatusEnum,
        send_date: Optional[datetime] = None,
    ) -> Optional[EmailSend]:
        """更新邮件发送状态"""
        db_obj = self.get(db, id=email_id)
        if not db_obj:
            return None

        update_data = {"send_status": status}
        if send_date:
            update_data["send_date"] = send_date
        elif status == EmailSendStatusEnum.SENT and not db_obj.send_date:
            update_data["send_date"] = datetime.utcnow()

        return self.update(db, db_obj=db_obj, obj_in=update_data)

    def search_emails(
        self,
        db: Session,
        *,
        kol_id: Optional[int] = None,
        platform: Optional[PlatformEnum] = None,
        status: Optional[EmailSendStatusEnum] = None,
        template_code: Optional[str] = None,
        project_code: Optional[str] = None,
        from_email: Optional[str] = None,
        to_email: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[EmailSend]:
        """搜索邮件发送记录"""
        query = db.query(EmailSend)

        if kol_id:
            query = query.filter(EmailSend.kol_id == kol_id)

        if platform:
            query = query.filter(EmailSend.platform == platform)

        if status:
            query = query.filter(EmailSend.send_status == status)

        if template_code:
            query = query.filter(EmailSend.template_code == template_code)

        if project_code:
            query = query.filter(EmailSend.project_code == project_code)

        if from_email:
            query = query.filter(EmailSend.from_email.ilike(f"%{from_email}%"))

        if to_email:
            query = query.filter(EmailSend.to_email.ilike(f"%{to_email}%"))

        return (
            query.order_by(EmailSend.created_at.desc()).offset(skip).limit(limit).all()
        )

    def get_email_statistics(
        self, db: Session, *, project_code: Optional[str] = None
    ) -> dict:
        """获取邮件发送统计信息"""
        query = db.query(EmailSend)

        if project_code:
            query = query.filter(EmailSend.project_code == project_code)

        total = query.count()
        pending = query.filter(
            EmailSend.send_status == EmailSendStatusEnum.PENDING
        ).count()
        sent = query.filter(EmailSend.send_status == EmailSendStatusEnum.SENT).count()
        failed = query.filter(
            EmailSend.send_status == EmailSendStatusEnum.FAILED
        ).count()

        # 按平台统计
        platform_stats = {}
        for platform in PlatformEnum:
            count = query.filter(EmailSend.platform == platform).count()
            platform_stats[platform.value] = count

        return {
            "total": total,
            "pending": pending,
            "sent": sent,
            "failed": failed,
            "success_rate": round(sent / total * 100, 2) if total > 0 else 0,
            "platform_stats": platform_stats,
        }


# 创建CRUD实例
email_send_logs = CRUDEmailSend(EmailSend)
