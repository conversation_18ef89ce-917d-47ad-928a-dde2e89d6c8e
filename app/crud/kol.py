"""
KOL CRUD操作
"""

from datetime import datetime, timezone
from decimal import Decimal
from typing import List, Optional

from sqlalchemy import String, cast, or_
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.enums import EmailFetchStatusEnum, KOLTierEnum, PlatformEnum
from app.models.kol import KOL
from app.schemas.kol import KOLCreate, KOLUpdate
from app.utils.tools import calculate_tier


class CRUDKOL(CRUDBase[KOL, KOLCreate, KOLUpdate]):
    """KOL CRUD操作类"""

    def get_by_kol_id(self, db: Session, *, kol_id: int) -> Optional[KOL]:
        """根据kol主键获取KOL"""
        return db.query(KOL).filter(KOL.id == kol_id).first()

    def get_by_social_id_only(self, db: Session, *, social_id: str) -> Optional[KOL]:
        """仅根据社交媒体ID获取KOL（可能返回多个，取第一个）"""
        return db.query(KOL).filter(KOL.social_id == social_id).first()

    def get_all_by_social_id(self, db: Session, *, social_id: str) -> List[KOL]:
        """根据社交媒体ID获取所有匹配的KOL记录"""
        return (
            db.query(KOL)
            .filter(KOL.social_id == social_id)
            .order_by(KOL.created_at.desc())
            .all()
        )

    def get_by_social_id_and_project(
        self, db: Session, *, social_id: str, project_code: str
    ) -> Optional[KOL]:
        """根据社交媒体ID和项目代码获取KOL（可能返回多个，取第一个）"""
        return (
            db.query(KOL)
            .filter(KOL.social_id == social_id, KOL.project_code == project_code)
            .first()
        )

    def get_by_social_id_platform_project(
        self, db: Session, *, social_id: str, platform: PlatformEnum, project_code: str
    ) -> Optional[KOL]:
        """根据社交媒体ID、平台和项目代码获取KOL"""
        return (
            db.query(KOL)
            .filter(
                KOL.social_id == social_id,
                KOL.platform == platform,
                KOL.project_code == project_code,
            )
            .first()
        )

    def get_by_project(
        self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[KOL]:
        """根据项目代码获取KOL列表 - 使用idx_kols_project_code索引"""
        return (
            db.query(KOL)
            .filter(KOL.project_code == project_code)
            .order_by(KOL.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_platform(
        self,
        db: Session,
        *,
        platform: PlatformEnum,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[KOL]:
        """根据平台获取KOL - 优化使用idx_kols_project_platform复合索引"""
        if project_code:
            # 使用复合索引 idx_kols_project_platform (project_code, platform)
            query = db.query(KOL).filter(
                KOL.project_code == project_code, KOL.platform == platform
            )
        else:
            # 使用单列索引 idx_kols_platform
            query = db.query(KOL).filter(KOL.platform == platform)

        return query.order_by(KOL.created_at.desc()).offset(skip).limit(limit).all()

    def get_by_tier(
        self,
        db: Session,
        *,
        tier: KOLTierEnum,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[KOL]:
        """根据KOL分级获取KOL - 优化使用idx_kols_project_tier复合索引"""
        if project_code:
            # 使用复合索引 idx_kols_project_tier (project_code, tier)
            query = db.query(KOL).filter(
                KOL.project_code == project_code, KOL.tier == tier
            )
        else:
            # 使用单列索引 idx_kols_tier
            query = db.query(KOL).filter(KOL.tier == tier)

        return (
            query.order_by(KOL.followers_count.desc()).offset(skip).limit(limit).all()
        )

    def get_with_email(
        self,
        db: Session,
        *,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[KOL]:
        """获取有邮箱的KOL - 优化使用idx_kols_email索引"""
        if project_code:
            # 先按项目过滤，再按邮箱过滤
            query = db.query(KOL).filter(
                KOL.project_code == project_code, KOL.email.isnot(None), KOL.email != ""
            )
        else:
            # 使用邮箱索引 idx_kols_email
            query = db.query(KOL).filter(KOL.email.isnot(None), KOL.email != "")

        return query.order_by(KOL.created_at.desc()).offset(skip).limit(limit).all()

    def search_kols(
        self,
        db: Session,
        *,
        kol_id: Optional[int] = None,
        nick_name: Optional[str] = None,
        social_id: Optional[str] = None,
        platform: Optional[PlatformEnum] = None,
        tier: Optional[KOLTierEnum] = None,
        min_followers: Optional[int] = None,
        max_followers: Optional[int] = None,
        min_engagement_rate: Optional[Decimal] = None,
        max_engagement_rate: Optional[Decimal] = None,
        min_mean_views_k: Optional[Decimal] = None,
        max_mean_views_k: Optional[Decimal] = None,
        min_median_views_k: Optional[Decimal] = None,
        max_median_views_k: Optional[Decimal] = None,
        min_ai_score: Optional[Decimal] = None,
        max_ai_score: Optional[Decimal] = None,
        created_after: Optional[datetime] = None,
        created_before: Optional[datetime] = None,
        updated_after: Optional[datetime] = None,
        updated_before: Optional[datetime] = None,
        ai_matched: Optional[bool] = None,
        source: Optional[str] = None,
        email: Optional[str] = None,
        email_fetch_status: Optional[str] = None,
        bio: Optional[str] = None,
        hashtags: Optional[List[str]] = None,
        topics: Optional[List[str]] = None,
        captions: Optional[List[str]] = None,
        hashtags_fuzzy: Optional[List[str]] = None,
        topics_fuzzy: Optional[List[str]] = None,
        captions_fuzzy: Optional[List[str]] = None,
        project_code: Optional[str] = None,
        crawler_task_id: Optional[int] = None,
        has_email: Optional[bool] = None,
        has_bio_extracted_email: Optional[bool] = None,
        has_nano_extracted_email: Optional[bool] = None,
        is_nano_unlocked: Optional[bool] = None,
        is_ai_mismatched: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[KOL]:
        """高级搜索KOL - 优化索引使用顺序"""
        query = db.query(KOL)

        # 主键ID精确搜索 - 最高优先级
        if kol_id is not None:
            query = query.filter(KOL.id == kol_id)

        # 优先使用高选择性的索引字段
        if project_code:
            query = query.filter(KOL.project_code == project_code)

        # 使用复合索引优化
        if project_code and platform:
            # 利用 idx_kols_project_platform 复合索引
            query = query.filter(KOL.platform == platform)
        elif platform:
            # 使用单列索引 idx_kols_platform
            query = query.filter(KOL.platform == platform)

        if project_code and tier:
            # 利用 idx_kols_project_tier 复合索引
            query = query.filter(KOL.tier == tier)
        elif tier:
            # 使用单列索引 idx_kols_tier
            query = query.filter(KOL.tier == tier)

        # 粉丝数范围查询 - 使用 idx_kols_followers_count 索引
        if min_followers is not None:
            query = query.filter(KOL.followers_count >= min_followers)
        if max_followers is not None:
            query = query.filter(KOL.followers_count <= max_followers)

        # 邮箱过滤 - 使用 idx_kols_email 索引
        if has_email is not None:
            if has_email:
                query = query.filter(
                    KOL.email.isnot(None), KOL.email != "", KOL.email != "null"
                )
            else:
                query = query.filter(
                    or_(KOL.email.is_(None), KOL.email == "", KOL.email == "null")
                )

        # 爬虫任务ID过滤
        if crawler_task_id is not None:
            query = query.filter(KOL.crawler_task_id == crawler_task_id)

        # 时间范围过滤 - 使用 idx_kols_created_at 和 idx_kols_updated_at 索引
        if created_after is not None:
            query = query.filter(KOL.created_at >= created_after)
        if created_before is not None:
            query = query.filter(KOL.created_at <= created_before)
        if updated_after is not None:
            query = query.filter(KOL.updated_at >= updated_after)
        if updated_before is not None:
            query = query.filter(KOL.updated_at <= updated_before)

        # 文本搜索（选择性较低，放在后面）
        if nick_name:
            query = query.filter(KOL.nick_name.ilike(f"%{nick_name}%"))

        if social_id:
            query = query.filter(KOL.social_id.ilike(f"%{social_id}%"))

        if source:
            query = query.filter(KOL.source.ilike(f"%{source}%"))

        if email:
            query = query.filter(KOL.email.ilike(f"%{email}%"))

        if bio:
            query = query.filter(KOL.bio.ilike(f"%{bio}%"))

        # 邮箱获取状态过滤
        if email_fetch_status:
            query = query.filter(KOL.email_fetch_status == email_fetch_status)

        # AI相关过滤
        if ai_matched is not None:
            query = query.filter(KOL.ai_matched == ai_matched)

        # 数值范围查询
        if min_engagement_rate is not None:
            query = query.filter(KOL.engagement_rate >= min_engagement_rate)
        if max_engagement_rate is not None:
            query = query.filter(KOL.engagement_rate <= max_engagement_rate)

        # 观看数范围查询
        if min_mean_views_k is not None:
            query = query.filter(KOL.mean_views_k >= min_mean_views_k)
        if max_mean_views_k is not None:
            query = query.filter(KOL.mean_views_k <= max_mean_views_k)

        if min_median_views_k is not None:
            query = query.filter(KOL.median_views_k >= min_median_views_k)
        if max_median_views_k is not None:
            query = query.filter(KOL.median_views_k <= max_median_views_k)

        # AI评分范围查询
        if min_ai_score is not None:
            query = query.filter(KOL.ai_score >= min_ai_score)
        if max_ai_score is not None:
            query = query.filter(KOL.ai_score <= max_ai_score)

        # 特定邮箱来源过滤
        if has_bio_extracted_email is not None:
            if has_bio_extracted_email:
                query = query.filter(
                    KOL.bio_extracted_email.isnot(None), KOL.bio_extracted_email != ""
                )
            else:
                query = query.filter(
                    or_(
                        KOL.bio_extracted_email.is_(None), KOL.bio_extracted_email == ""
                    )
                )

        if has_nano_extracted_email is not None:
            if has_nano_extracted_email:
                query = query.filter(
                    KOL.nano_extracted_email.isnot(None), KOL.nano_extracted_email != ""
                )
            else:
                query = query.filter(
                    or_(
                        KOL.nano_extracted_email.is_(None),
                        KOL.nano_extracted_email == "",
                    )
                )

        # 新增：nano解锁状态过滤（基于nano_email_fetched_at字段）
        if is_nano_unlocked is not None:
            if is_nano_unlocked:
                # 有时间戳表示已解锁
                query = query.filter(KOL.nano_email_fetched_at.isnot(None))
            else:
                # 没有时间戳表示未解锁
                query = query.filter(KOL.nano_email_fetched_at.is_(None))

        # 新增：AI mismatch状态过滤（基于ai_scored_at字段）
        if is_ai_mismatched is not None:
            if is_ai_mismatched:
                # 有时间戳表示已进行过AI评分（可能mismatch）
                query = query.filter(KOL.ai_scored_at.isnot(None))
            else:
                # 没有时间戳表示未进行过AI评分
                query = query.filter(KOL.ai_scored_at.is_(None))

        # JSONB查询（成本较高，放在最后）
        # 精确匹配查询
        if hashtags:
            for hashtag in hashtags:
                query = query.filter(KOL.hashtags.contains([hashtag]))
        if topics:
            for topic in topics:
                query = query.filter(KOL.topics.contains([topic]))
        if captions:
            for caption in captions:
                query = query.filter(KOL.captions.contains([caption]))

        # 模糊匹配查询（支持多关键词，使用 JSONB 操作符优化）
        if hashtags_fuzzy:
            # 支持多个关键词的模糊搜索，任意一个匹配即可
            hashtag_conditions = []
            for keyword in hashtags_fuzzy:
                hashtag_conditions.append(
                    cast(KOL.hashtags, String).ilike(f"%{keyword}%")
                )
            if hashtag_conditions:
                query = query.filter(or_(*hashtag_conditions))

        if topics_fuzzy:
            # 支持多个关键词的模糊搜索，任意一个匹配即可
            topic_conditions = []
            for keyword in topics_fuzzy:
                topic_conditions.append(cast(KOL.topics, String).ilike(f"%{keyword}%"))
            if topic_conditions:
                query = query.filter(or_(*topic_conditions))

        if captions_fuzzy:
            # 支持多个关键词的模糊搜索，任意一个匹配即可
            caption_conditions = []
            for keyword in captions_fuzzy:
                caption_conditions.append(
                    cast(KOL.captions, String).ilike(f"%{keyword}%")
                )
            if caption_conditions:
                query = query.filter(or_(*caption_conditions))

        return query.order_by(KOL.updated_at.desc()).offset(skip).limit(limit).all()

    def get_kol_statistics(
        self, db: Session, *, project_code: Optional[str] = None
    ) -> dict:
        """获取KOL统计信息"""
        query = db.query(KOL)

        if project_code:
            query = query.filter(KOL.project_code == project_code)

        total = query.count()
        with_email = query.filter(KOL.email.isnot(None), KOL.email != "").count()

        # 按平台统计
        platform_stats = {}
        for platform in PlatformEnum:
            count = query.filter(KOL.platform == platform).count()
            platform_stats[platform.value] = count

        # 按分级统计
        tier_stats = {}
        for tier in KOLTierEnum:
            count = query.filter(KOL.tier == tier).count()
            tier_stats[tier.value] = count

        return {
            "total": total,
            "with_email": with_email,
            "without_email": total - with_email,
            "platform_stats": platform_stats,
            "tier_stats": tier_stats,
        }

    def create_kol(self, db: Session, *, obj_in: KOLCreate) -> KOL:
        """创建KOL，检查social_id唯一性"""
        # 检查social_id是否已存在
        existing = self.get_by_social_id_platform_project(
            db,
            social_id=obj_in.social_id,
            platform=obj_in.platform,
            project_code=obj_in.project_code,
        )
        if existing:
            raise ValueError(f"KOL social_id '{obj_in.social_id}' 已存在")
        # 如果有粉丝数，根据粉丝数自动计算tier并填充obj_in
        if obj_in.followers_count and not obj_in.tier:
            obj_in.tier = KOLTierEnum(calculate_tier(obj_in.followers_count))

        return self.create(db, obj_in=obj_in)

    # Email获取状态相关方法
    def update_bio_parsed_status(
        self, db: Session, *, kol_id: int, extracted_email: Optional[str] = None
    ) -> Optional[KOL]:
        """更新bio解析状态"""
        kol_obj = self.get_by_kol_id(db, kol_id=kol_id)
        if not kol_obj:
            return None

        update_data = {
            "bio_parsed_at": datetime.now(timezone.utc),
            "email_fetch_status": EmailFetchStatusEnum.BIO_PARSED,
        }
        if extracted_email:
            update_data["bio_extracted_email"] = extracted_email
            update_data["email"] = extracted_email  # 同时更新主邮箱字段
            update_data["email_fetch_status"] = (
                EmailFetchStatusEnum.COMPLETED
            )  # 如果找到邮箱，直接完成

        return self.update(db, db_obj=kol_obj, obj_in=update_data)

    def update_ai_score_status(
        self, db: Session, *, kol_id: int, ai_score: Decimal
    ) -> Optional[KOL]:
        """更新AI评分状态"""
        kol_obj = self.get_by_kol_id(db, kol_id=kol_id)
        if not kol_obj:
            return None

        # 根据评分计算是否匹配（当前标准：分数>0就匹配）
        ai_matched = ai_score > 0 if ai_score is not None else False

        update_data = {
            "ai_score": ai_score,
            "ai_matched": ai_matched,
            "ai_scored_at": datetime.now(timezone.utc),
            "email_fetch_status": EmailFetchStatusEnum.AI_SCORED,
        }

        return self.update(db, db_obj=kol_obj, obj_in=update_data)

    def update_nano_email_status(
        self,
        db: Session,
        *,
        kol_id: int,
        extracted_email: Optional[str] = None,
        topics: Optional[List[str]] = None,
    ) -> Optional[KOL]:
        """更新nano邮箱获取状态和topics信息"""
        kol_obj = self.get_by_kol_id(db, kol_id=kol_id)
        if not kol_obj:
            return None

        update_data = {
            "nano_email_fetched_at": datetime.now(timezone.utc),
            "email_fetch_status": EmailFetchStatusEnum.NANO_FETCHED,
        }
        if extracted_email:
            update_data["nano_extracted_email"] = extracted_email
            update_data["email"] = extracted_email  # 同时更新主邮箱字段
            update_data["email_fetch_status"] = (
                EmailFetchStatusEnum.COMPLETED
            )  # 如果找到邮箱，直接完成

        # 合并topics信息
        if topics:
            existing_topics = kol_obj.topics or []
            # 合并并去重topics
            merged_topics = list(set(existing_topics + topics))
            update_data["topics"] = merged_topics

        return self.update(db, db_obj=kol_obj, obj_in=update_data)

    def mark_email_fetch_completed(self, db: Session, *, kol_id: int) -> Optional[KOL]:
        """标记邮箱获取流程完成"""
        kol_obj = self.get_by_kol_id(db, kol_id=kol_id)
        if not kol_obj:
            return None

        update_data = {"email_fetch_status": EmailFetchStatusEnum.COMPLETED}
        return self.update(db, db_obj=kol_obj, obj_in=update_data)

    def get_kols_need_email_fetch(
        self, db: Session, *, project_code: Optional[str] = None, limit: int = 100
    ) -> List[KOL]:
        """获取需要进行邮箱获取的KOL列表"""
        query = db.query(KOL).filter(
            or_(
                KOL.email_fetch_status.is_(None),
                KOL.email_fetch_status == EmailFetchStatusEnum.PENDING,
            ),
            KOL.email.is_(None),  # 只处理没有邮箱的KOL
        )

        if project_code:
            query = query.filter(KOL.project_code == project_code)

        return query.order_by(KOL.created_at.desc()).limit(limit).all()

    def get_by_email_fetch_status(
        self,
        db: Session,
        *,
        status: str,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[KOL]:
        """根据邮箱获取状态查询KOL"""
        query = db.query(KOL).filter(KOL.email_fetch_status == status)

        if project_code:
            query = query.filter(KOL.project_code == project_code)

        return query.order_by(KOL.created_at.desc()).offset(skip).limit(limit).all()

    def get_by_ai_matched_status(
        self,
        db: Session,
        *,
        ai_matched: bool,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[KOL]:
        """根据AI匹配状态查询KOL"""
        query = db.query(KOL).filter(KOL.ai_matched == ai_matched)

        if project_code:
            query = query.filter(KOL.project_code == project_code)

        return query.order_by(KOL.created_at.desc()).offset(skip).limit(limit).all()

    def get_matched_kols(
        self,
        db: Session,
        *,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[KOL]:
        """获取AI匹配的KOL列表"""
        return self.get_by_ai_matched_status(
            db, ai_matched=True, project_code=project_code, skip=skip, limit=limit
        )

    def get_unmatched_kols(
        self,
        db: Session,
        *,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[KOL]:
        """获取AI未匹配的KOL列表"""
        return self.get_by_ai_matched_status(
            db, ai_matched=False, project_code=project_code, skip=skip, limit=limit
        )


# 创建CRUD实例
kol = CRUDKOL(KOL)
