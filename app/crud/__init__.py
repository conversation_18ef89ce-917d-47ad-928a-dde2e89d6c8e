# Base CRUD class
from app.crud import kol_performance_view
from app.crud.base import CRUDBase
from app.crud.candidate import candidate
from app.crud.crawler_task import crawler_task
from app.crud.email_send_logs import email_send_logs
from app.crud.email_template import email_template
from app.crud.kol import kol
from app.crud.payment import payment
from app.crud.performance import performance

# V2 CRUD imports - 基于新的数据模型
from app.crud.project import project

# Legacy CRUD imports - 保持向后兼容 (注释掉不存在的文件)
# from app.crud.candidate_data import candidate_data
# from app.crud.collaboration_performance import collaboration_performance
# from app.crud.filter_data import filter_data
# from app.crud.filter_kol_association import filter_kol_association
# from app.crud.kol_info import kol_info
# from app.crud.send_data import send_data
# from app.crud.video_info import video_info
# from app.crud.tag import tag
# from app.crud.kol_tag_association import kol_tag_association
# from app.crud.project_tag_association import project_tag_association

__all__ = [
    # Base class
    "CRUDBase",
    # V2 CRUD instances
    "project",
    "email_template",
    "crawler_task",
    "kol",
    "email_send_logs",
    "candidate",
    "performance",
    "payment",
    "kol_performance_view",
    # Legacy CRUD instances (注释掉不存在的)
    # "filter_data",
    # "filter_kol_association",
    # "kol_info",
    # "send_data",
    # "video_info",
    # "candidate_data",
    # "collaboration_performance",
    # "tag",
    # "kol_tag_association",
    # "project_tag_association"
]
