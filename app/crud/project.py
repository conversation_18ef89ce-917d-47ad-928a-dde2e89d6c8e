"""
项目CRUD操作
"""

from typing import List, Optional

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectUpdate


class CRUDProject(CRUDBase[Project, ProjectCreate, ProjectUpdate]):
    """项目CRUD操作类"""

    def get_by_code(self, db: Session, *, code: str) -> Optional[Project]:
        """根据项目代码获取项目"""
        return db.query(Project).filter(Project.code == code).first()

    def get_multi_by_name(
        self, db: Session, *, name_pattern: str, skip: int = 0, limit: int = 100
    ) -> List[Project]:
        """根据项目名称模糊查询"""
        return (
            db.query(Project)
            .filter(Project.name.ilike(f"%{name_pattern}%"))
            .order_by(Project.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create_project(self, db: Session, *, obj_in: ProjectCreate) -> Project:
        """创建项目，检查代码唯一性"""
        # 检查项目代码是否已存在
        existing = self.get_by_code(db, code=obj_in.code)
        if existing:
            raise ValueError(f"项目代码 '{obj_in.code}' 已存在")

        return self.create(db, obj_in=obj_in)

    def update_project(
        self, db: Session, *, code: str, obj_in: ProjectUpdate
    ) -> Optional[Project]:
        """根据项目代码更新项目"""
        db_obj = self.get_by_code(db, code=code)
        if not db_obj:
            return None

        return self.update(db, db_obj=db_obj, obj_in=obj_in)

    def delete_project(self, db: Session, *, code: str) -> Optional[Project]:
        """根据项目代码删除项目"""
        db_obj = self.get_by_code(db, code=code)
        if not db_obj:
            return None

        db.delete(db_obj)
        db.commit()
        return db_obj


# 创建CRUD实例
project = CRUDProject(Project)
