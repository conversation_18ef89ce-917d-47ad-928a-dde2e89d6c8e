import asyncio
import json
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

import requests
from openai import OpenAI
from sqlalchemy.orm import Session

from app.crud.kol import kol as kol_crud
from app.logging_config import get_logger

# 使用任务日志记录器
logger = get_logger("app.service.llm_service")


def email_and_keywords_from_bio(bio_text: str = "") -> Tu<PERSON>[List[str], str]:
    """
    使用 OpenAI 从 bio 文本中提取关键词

    Args:
        bio_text: 要分析的 bio 文本

    Returns:
        关键词列表，如果处理失败则返回空列表
    """
    if not bio_text or not isinstance(bio_text, str):
        logger.warning(f"bio_text: [{bio_text}]:简介为空或者不是字符串")
        return [], ""

    try:
        client = OpenAI(
            api_key="sk-srqzisugylaszaqrxmskqopgrqjtotyaaljquhlzawdsgfqa",
            base_url="https://api.siliconflow.cn/v1",
        )
        # 使用 OpenAI 提取关键词
        response = client.chat.completions.create(
            model="deepseek-ai/DeepSeek-V3",
            messages=[
                {
                    "role": "system",
                    "content": "你是一名营销专家。请根据这位网红的简介 (bio) 总结其内容的主要话题和摘取email信息。主要话题限制为三个,每个话题用一个词概括。",
                },
                {
                    "role": "user",
                    "content": """
                    要求：
                    - 如果简介不包含相关信息（例如，只有姓名、大学、邮箱等），则不进行总结。
                    - 尽可能使用网红的原文。
                    - 常用话题包括：健身 (fitness)、生活方式 (lifestyle)、饮食 (diet)、健康 (health)、教练 (coach)（可供使用）。
                    - 使用简介中的原语种输出。
                    - 提取简介中的邮箱, 如果没有邮箱返回空字符串 ""。
                    - 注意如果简介中不包含邮箱，一定要返回空字符串"", 不能凭空捏造邮箱（这很重要）。
                    - 如果简介中只存在邮箱, topics话题返回空数组即可。

                    输出格式：
                    {
                        "topics":["Topic 1", "Topic 2", "Topic 3"],
                        "email": "<EMAIL>"
                    }
                    如果没有相关话题或者email,则输出:
                    {
                        "topics":[],
                        "email": ""
                    }
                    以下是网红的简介：
                    bio_text: %s
                    """
                    % bio_text,
                },
            ],
            response_format={"type": "json_object"},
        )

        # 解析响应数据
        result = response.choices[0].message.content
        if result is None:
            logger.warning("大模型返回的内容为空")
            return [], ""
        json_data = json.loads(result)
        topics = json_data.get("topics", [])
        email = json_data.get("email", "")
        logger.info(f"大模型从 {bio_text} 中提取topic {topics} 和 email: {email}")
        return topics, email
    except Exception as e:
        logger.error(f"提取关键词时发生错误: {type(e).__name__}: {str(e)}")
        return [], ""


def call_gemini(system_role: str, content: str) -> Optional[str]:
    """
    调用Gemini API进行KOL匹配评估

    Args:
        system_role: 系统角色提示词
        content: 用户内容

    Returns:
        评估结果分数字符串，失败返回None
    """
    url = "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer AIzaSyB9nxsAvlZ1FKtPVoG8YJATht6cCTVMBC4",
    }
    payload = {
        "model": "gemini-2.0-flash",
        "messages": [
            {"role": "system", "content": system_role},
            {"role": "user", "content": content},
        ],
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        data = response.json()
        result = data["choices"][0]["message"]["content"].strip()
        logger.info(f"Gemini API调用成功，返回结果: {result}")
        return result
    except Exception as e:
        logger.error(f"调用Gemini接口失败: {e}")
        return None


async def async_call_gemini(system_role: str, content: str) -> Optional[str]:
    """
    异步调用Gemini API进行KOL匹配评估
    由于异步HTTP请求可能有地理位置限制，这里使用同步调用的方式

    Args:
        system_role: 系统角色提示词
        content: 用户内容

    Returns:
        评估结果分数字符串，失败返回None
    """
    # 暂时使用同步调用来避免地理位置限制问题
    # 在asyncio事件循环中运行同步函数
    loop = asyncio.get_event_loop()
    try:
        result = await loop.run_in_executor(None, call_gemini, system_role, content)
        return result
    except Exception as e:
        logger.error(f"异步调用Gemini接口失败: {e}")
        return None


def call_gpt4(system_role: str, content: str) -> Optional[str]:
    """
    调用GPT-4 API进行KOL匹配评估（备用）

    Args:
        system_role: 系统角色提示词
        content: 用户内容

    Returns:
        评估结果分数字符串，失败返回None
    """
    url = "https://api.openai.com/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-svcacct-xxxxxx",  # 需要配置真实的API Key
    }
    payload = {
        "model": "gpt-4.1-mini-2025-04-14",
        "messages": [
            {"role": "system", "content": system_role},
            {"role": "user", "content": content},
        ],
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        data = response.json()
        result = data["choices"][0]["message"]["content"].strip()
        logger.info(f"GPT-4 API调用成功，返回结果: {result}")
        return result
    except Exception as e:
        logger.error(f"调用OpenAI接口失败: {e}")
        return None


def fetch_kol_hashtags(db: Session, kol_id: int) -> List[str]:
    """
    从KOL主表获取hashtag数据

    Args:
        db: 数据库会话
        kol_id: KOL主键ID

    Returns:
        hashtag列表
    """
    try:
        kol = kol_crud.get(db, id=kol_id)
        if not kol:
            logger.warning(f"未找到KOL: {kol_id}")
            return []

        hashtags = kol.hashtags or []
        if not isinstance(hashtags, list):
            logger.warning(f"KOL {kol_id} 的hashtags字段不是列表类型: {type(hashtags)}")
            return []

        # 过滤和清理hashtag
        cleaned_hashtags = [
            tag.strip() for tag in hashtags if isinstance(tag, str) and tag.strip()
        ]

        logger.info(f"获取KOL {kol_id} 的hashtags: {len(cleaned_hashtags)} 个")
        return cleaned_hashtags

    except Exception as e:
        logger.error(f"获取KOL {kol_id} hashtags失败: {e}")
        return []


def evaluate_kol_alignment(
    db: Session, kol_id: int, prompt_text: str
) -> Optional[Decimal]:
    """
    评估KOL与特定需求的匹配度

    Args:
        db: 数据库会话
        kol_id: KOL ID
        prompt_text: 评估提示词

    Returns:
        匹配分数(0-100)，失败返回None
    """
    try:
        # 获取KOL的hashtag数据
        hashtags = fetch_kol_hashtags(db, kol_id)
        if not hashtags:
            raise ValueError(f"KOL {kol_id} 没有有效的hashtag数据")

        # 构建hashtag信息字符串
        hashtag_info_str = f"KOL的hashtags: {', '.join(hashtags)}"

        logger.info(f"KOL {kol_id} hashtag信息: {hashtag_info_str}")

        # 调用大模型进行评估
        result = call_gemini(prompt_text, hashtag_info_str)

        if result is None:
            logger.error(f"大模型调用失败: kol_id={kol_id}")
            return None

        # 尝试解析分数
        try:
            # 提取数字分数
            import re

            score_match = re.search(r"\d+", result)
            if score_match:
                score = Decimal(score_match.group())
                if 0 <= score <= 100:
                    return score
                else:
                    logger.warning(f"分数超出范围: {score}, kol_id={kol_id}")
                    return None
            else:
                logger.warning(f"无法从结果中提取分数: {result}, kol_id={kol_id}")
                return None
        except (ValueError, TypeError) as e:
            logger.error(f"分数解析失败: {result}, error={e}, kol_id={kol_id}")
            return None

    except Exception as e:
        logger.error(f"evaluate_kol_alignment异常: {e}, kol_id={kol_id}")
        return None


async def async_evaluate_kol_alignment(
    db: Session, kol_id: int, prompt_text: str
) -> Tuple[Optional[Decimal], str]:
    """
    异步评估KOL与特定需求的匹配度

    Args:
        db: 数据库会话
        kol_id: KOL ID
        prompt_text: 评估提示词

    Returns:
        匹配分数(0-100)，失败返回None
    """
    try:
        # 获取KOL的hashtag数据
        hashtags = fetch_kol_hashtags(db, kol_id)
        if not hashtags:
            raise ValueError(f"KOL {kol_id} 没有有效的hashtag数据")

        # 构建hashtag信息字符串
        hashtag_info_str = ",".join(hashtags)

        logger.info(f"KOL {kol_id} hashtag信息: {hashtag_info_str}")

        # 异步调用大模型进行评估
        result = await async_call_gemini(prompt_text, hashtag_info_str)

        if result is None:
            msg = f"大模型异步调用失败: kol_id={kol_id}"
            logger.error(msg)
            raise Exception(msg)

        # 尝试解析分数
        try:
            # 提取数字分数
            import re

            score_match = re.search(r"\d+", result)
            if score_match:
                score = Decimal(score_match.group())
                if 0 <= score <= 100:
                    return score, "评估成功"
                else:
                    raise Exception(f"分数超出范围: {score}, kol_id={kol_id}")
            else:
                raise Exception(f"无法从结果中提取分数: {result}, kol_id={kol_id}")
        except (ValueError, TypeError) as e:
            raise Exception(f"分数解析失败: {result}, error={e}, kol_id={kol_id}")

    except Exception as e:
        logger.error(f"async_evaluate_kol_alignment异常: {e}, kol_id={kol_id}")
        return None, str(e)


class KOLMatchService:
    """KOL匹配评估服务类"""

    def __init__(self, db: Session):
        self.db = db

    def evaluate_single_kol(
        self, kol_id: int, prompt: str, save_to_db: bool = True
    ) -> Dict[str, Any]:
        """
        评估单个KOL的匹配度

        Args:
            kol_id: KOL ID
            prompt: 评估提示词
            save_to_db: 是否保存到数据库

        Returns:
            评估结果字典
        """
        try:
            # 进行评估（传入数据库会话）
            score = evaluate_kol_alignment(self.db, kol_id, prompt)

            if score is None:
                return {
                    "kol_id": kol_id,
                    "score": None,
                    "prompt": prompt,
                    "success": False,
                    "message": "评估失败",
                }

            # 保存到数据库
            if save_to_db:
                try:
                    kol_crud.update_ai_score_status(
                        self.db, kol_id=kol_id, ai_score=score
                    )
                    logger.info(f"KOL {kol_id} 评分已保存到数据库: {score}")
                except Exception as e:
                    logger.error(f"保存评分到数据库失败: {e}")
                    return {
                        "kol_id": kol_id,
                        "score": score,
                        "prompt": prompt,
                        "success": False,
                        "message": f"评估成功但保存失败: {str(e)}",
                    }

            return {
                "kol_id": kol_id,
                "score": score,
                "prompt": prompt,
                "success": True,
                "message": "评估成功",
            }

        except Exception as e:
            logger.error(f"评估KOL {kol_id} 时发生异常: {e}")
            return {
                "kol_id": kol_id,
                "score": None,
                "prompt": prompt,
                "success": False,
                "message": f"评估异常: {str(e)}",
            }

    def evaluate_batch_kols(
        self,
        kol_ids: List[int],
        prompt: str,
        max_workers: int = 10,
        save_to_db: bool = True,
    ) -> Dict[str, Any]:
        """
        批量评估KOL的匹配度

        Args:
            kol_ids: KOL ID列表
            prompt: 评估提示词
            max_workers: 最大并发数
            save_to_db: 是否保存到数据库

        Returns:
            批量评估结果
        """
        results = []
        success_count = 0
        failed_count = 0

        logger.info(f"开始批量评估 {len(kol_ids)} 个KOL，并发数: {max_workers}")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_kol = {
                executor.submit(
                    self.evaluate_single_kol, kol_id, prompt, save_to_db
                ): kol_id
                for kol_id in kol_ids
            }

            # 收集结果
            for future in as_completed(future_to_kol):
                kol_id = future_to_kol[future]
                try:
                    result = future.result()
                    results.append(result)

                    if result["success"]:
                        success_count += 1
                        logger.info(f"KOL {kol_id} 评估成功: {result['score']}")
                    else:
                        failed_count += 1
                        logger.warning(f"KOL {kol_id} 评估失败: {result['message']}")

                except Exception as e:
                    failed_count += 1
                    error_result = {
                        "kol_id": kol_id,
                        "score": None,
                        "prompt": prompt,
                        "success": False,
                        "message": f"处理异常: {str(e)}",
                    }
                    results.append(error_result)
                    logger.error(f"处理KOL {kol_id} 时发生异常: {e}")

        logger.info(
            f"批量评估完成 - 总数: {len(kol_ids)}, 成功: {success_count}, 失败: {failed_count}"
        )

        return {
            "total_count": len(kol_ids),
            "success_count": success_count,
            "failed_count": failed_count,
            "results": results,
        }

    # def get_kol_hashtags(self, kol_id: int) -> Dict[str, Any]:
    #     """
    #     获取KOL的hashtag数据

    #     Args:
    #         kol_id: KOL ID

    #     Returns:
    #         hashtag数据结果
    #     """
    #     try:
    #         hashtags = fetch_kol_hashtags(self.db, kol_id)

    #         return {
    #             "kol_id": kol_id,
    #             "hashtag_count": len(hashtags),
    #             "hashtags": hashtags,
    #             "success": True,
    #             "message": "获取成功"
    #         }

    #     except Exception as e:
    #         logger.error(f"获取KOL {kol_id} hashtag数据失败: {e}")
    #         return {
    #             "kol_id": kol_id,
    #             "hashtag_count": 0,
    #             "hashtags": [],
    #             "success": False,
    #             "message": f"获取失败: {str(e)}"
    #         }


# 便捷函数
def create_kol_match_service(db: Session) -> KOLMatchService:
    """创建KOL匹配服务实例"""
    return KOLMatchService(db)


# 示例用法
if __name__ == "__main__":
    # result, email = email_and_keywords_from_bio(
    # """
    # Twitch🎮｜chungchung030
    # Email💌｜<EMAIL>⁣
    #        🌱｜ @chungman_pr
    # """)
    # print(result)
    # print(email)
    prompt = """
角色：顶级海外TikTok/Instagram KOL营销专家
目标：对TikTok创作者进行全面的适宜性评估，以筛选其参与开发并推广一款专注于健身、锻炼和健康生活方式的app。该app旨在提供个性化训练计划、跟练视频、进度跟踪和社区互动，帮助用户高效实现健身目标并变现。
任务：基于以下详细评估标准，为每位创作者分配一个从0到100分的综合适宜性评分。在进行任何扣分前，若正面得分的总和超过100分，则最终分数上限为100分。
评估细则与评分体系：
标签策略与受众定位 (潜在最高分：25分)
每个与核心健身主题和特定训练方法直接相关的标签 +3分（例如：#fitness, #workout, #gym, #healthylifestyle, #training, #fitnessmotivation, #bodybuilding, #cardio, #strengthtraining, #(具体训练类型如 #hiit, #yoga, #running, #weightloss, #musclegain)）。
每个与更广泛的健康、生活方式或锻炼文化主题相符的标签 +1分（例如：#health, #exercise, #wellness, #nutrition, #fitfam, #gymlife, #motivation, #lifestyle, #fitnessjourney, #results）。
内容与App协同性及价值主张契合度 (潜在最高分：45分)
内容明确展示或讨论健身app如何赋能用户训练（易用性、个性化计划、实时跟踪） +10分（例如：“一键定制你的专属训练”、“跟练视频指导新手避免受伤”、“实时追踪进步保持动力”）。
内容显著展示app的核心功能或效果（如个性化计划生成、跟练视频质量、进度报告、社区挑战） +10分（例如：演示app使用流程、用户跟练成果对比、数据报告可视化）。
内容直接触及或清晰呼应健身爱好者的核心需求与痛点 +10分（例如：时间管理困难、缺乏专业性指导、动力不足、目标达成障碍）。
内容展示将健身app无缝融入日常训练或健康生活，提供实用、高价值场景，体现趣味性和变现潜力 +15分（重要加分项）（例如：直播整合app训练、用户生成内容分享、app如何解决真实瓶颈、粉丝挑战活动、消费意愿证据如购买讨论）。
受众画像与互动质量 (潜在最高分：15分)
如果受众评论、讨论及整体互动模式强烈表明存在大量健身爱好者、跟练用户或健康追求者，或明确表达了对训练工具、计划定制或付费内容的需求/兴趣，则 +10分。
健身相关内容的互动率（如点赞、分享、深入讨论技巧、索要计划细节、用户跟练反馈）持续较高且积极，表明社群高粘性、内容接受度高且具有消费能力，则 +5分。
内容焦点与品牌安全 (扣分与淘汰机制)
如果非核心内容（如完全不相关的美妆、旅行、娱乐Vlog）占比显著（占近期相关帖子的25-50%），且未能与健身或健康兴趣点有效结合，从而表明内容焦点分散或专业度不足，则 扣10分。
满足以下任一条件，则自动淘汰（最终得分：0分）：
创作者推广不科学、有害的健身方法（如极端节食、危险补剂）或虚假效果承诺。
内容 (>50%) 绝大部分与健身/健康无关（例如：主要发美食内容，偶尔插入锻炼视频），或与app核心功能及目标用户兴趣（健身训练跟练）存在根本性错位。
内容具有攻击性、歧视性、传播健康不实信息、包含高风险行为，或在其他方面不适宜且有损品牌形象。
首要评估原则：
优先考虑内容真实、专业地展示健身app作为一种高效、易用且激励性的解决方案，特别针对健身新手、忙碌人群、目标驱动者或高消费意愿用户。
展示app如何深度融入创作者的专业训练日常，提供具体应用场景和可量化成果，比单纯动作演示更具价值。
即使未明确提及“app”，但内容强烈暗示工具需求或完美契合场景（如反复求助训练计划、用户求购相关产品），也应视为高度契合。
强调用户跟练证据和变现潜力（如用户分享成果、付费意愿评论），确保高质量内容驱动粘性和转化。
输出格式：
仅提供分析完所有内容的综合分数，只能以唯一数字出现，例如：xx
    """
    hashtags = ""
    print(
        call_gemini(
            prompt,
            "persona3,persona4,shinmegamitensei,atlus,persona5,talesofzestiria,kingoffighters,satsukikiryuin,anntakamaki,touhou",
        )
    )
