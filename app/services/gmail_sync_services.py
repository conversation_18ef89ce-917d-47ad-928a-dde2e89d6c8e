import concurrent.futures
import os
import pickle
import re
import sys
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple

import requests
from google.auth.transport.requests import Request

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import re

from app.crud.kol import kol as kol_crud
from app.crud.project import project as project_crud
from app.db.session import SessionLocal
from app.models.enums import FollowUpStatusEnum, PlatformEnum


def get_email_from_string(emails: str) -> str:
    """
    从字符串中提取第一个符合格式的邮箱地址

    参数:
        emails: 包含潜在邮箱的字符串

    返回:
        str: 提取到的第一个邮箱地址；无匹配时返回None
    """
    # 邮箱正则表达式，适配大多数常见格式
    email_pattern = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"

    # 查找第一个匹配的邮箱
    match = re.search(email_pattern, emails)

    # 返回第一个匹配结果，无匹配则返回None
    return match.group() if match else ""


def _is_valid_email(email):
    """验证邮箱格式是否有效"""
    import re

    if not email:
        return False
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(pattern, email) is not None


def _is_valid_social_link(link, platform):
    """验证社交媒体链接格式是否有效"""
    import re

    if not link:
        return False

    if platform == "tiktok":
        patterns = [
            r"tiktok\.com/@[^/?&\s>]+",
            r"www\.tiktok\.com/@[^/?&\s>]+",
        ]
    elif platform == "instagram":
        patterns = [
            r"instagram\.com/[^/?&\s>]+",
            r"www\.instagram\.com/[^/?&\s>]+",
        ]
    elif platform == "youtube":
        patterns = [
            r"youtube\.com/channel/[^/?&\s>]+",
            r"youtube\.com/c/[^/?&\s>]+",
            r"youtube\.com/@[^/?&\s>]+",
            r"www\.youtube\.com/channel/[^/?&\s>]+",
            r"www\.youtube\.com/c/[^/?&\s>]+",
            r"www\.youtube\.com/@[^/?&\s>]+",
        ]
    else:
        return False

    for pattern in patterns:
        if re.search(pattern, link, re.IGNORECASE):
            return True
    return False


def _clean_social_url_for_note(
    tiktok_url: str, parsed_social_link_data: Dict
) -> Optional[str]:
    """
    清理社交媒体URL，去除末尾的多余字符（如 > 符号）

    Args:
        tiktok_url: TikTok URL
        parsed_social_link_data: 解析的社交媒体链接数据

    Returns:
        清理后的note内容，如果没有URL则返回None
    """
    social_url = tiktok_url or parsed_social_link_data.get("extracted_from_email", "")
    if social_url:
        # 清理URL末尾的多余字符（如 > 符号、空格等）
        cleaned_url = social_url.rstrip("> \t\n\r")
        return f"社交媒体URL: {cleaned_url}"
    return None


def get_gmail_data(project_cfg, project_code: str, gmail_api: "GmailAPI") -> List[Dict]:
    SessionLocal()
    label = project_cfg.get("email_labels", "")
    sublabel = project_cfg.get("email_sublabels", "")
    tracker = project_cfg.get("tracker", "")
    all_labels = gmail_api.get_all_labels()
    matched_labels, matched_parent_labels, matched_sublabels = (
        gmail_api.filter_relevant_labels(all_labels, label, sublabel)
    )
    project_labels = gmail_api.get_project_and_sublabels(all_labels, project_code)
    if not matched_labels:
        print(f"项目 {project_code} 没有匹配的标签，跳过")
        return []

    print(f"匹配到 {len(matched_labels)} 个标签")
    label_to_unique_ids = gmail_api.process_labels_and_get_unique_threads(
        project_labels["project_label"]
    )

    # 只取前50条 用于调试
    label_to_unique_ids[project_code] = set(
        list(label_to_unique_ids[project_code])[:50]
    )
    # all_thread_details = gmail_api.fetch_thread_details(label_to_unique_ids)
    with open("/Users/<USER>/prjs/work/kol/scripts/all_thread_details.txt", "r") as f:
        all_thread_details = eval(f.read())

    final_data = to_candidate_records(all_thread_details, project_code, tracker)
    return final_data


def to_candidate_records(all_thread_details, project_code: str, tracker: str):
    import re
    import uuid

    candidate_records = []
    db = SessionLocal()

    try:
        for thread_detail in all_thread_details:
            try:
                # 提取基本信息
                thread_id = thread_detail.get("thread_id", "")
                tiktok_id = thread_detail.get("tiktok_id", "")
                tiktok_url = thread_detail.get("tiktok_url", "")
                messages = thread_detail.get("messages", [])
                label = thread_detail.get("label", "")

                if not thread_id or not messages:
                    continue

                # 提取邮件信息

                # 获取发件人邮箱（非自己发送的邮件）
                reply_email = None
                parsed_email_data = {}
                for msg in messages:
                    if not msg.get("is_self", False):
                        reply_email = get_email_from_string(msg.get("from", ""))
                        # 解析日期为标准格式
                        date_str = msg.get("date", "")
                        if date_str:
                            try:
                                # 如果date是时间戳（毫秒），转换为标准格式
                                if isinstance(date_str, (int, float)) or (
                                    isinstance(date_str, str) and date_str.isdigit()
                                ):
                                    timestamp = (
                                        int(date_str) / 1000
                                        if int(date_str) > 1e10
                                        else int(date_str)
                                    )
                                    parsed_date = datetime.fromtimestamp(
                                        timestamp, tz=timezone.utc
                                    )
                                    date_str = parsed_date.strftime("%Y-%m-%d %H:%M:%S")
                                # 如果已经是字符串格式，尝试解析并重新格式化
                                elif isinstance(date_str, str):
                                    try:
                                        # 尝试解析常见的日期格式
                                        parsed_date = datetime.fromisoformat(
                                            date_str.replace("Z", "+00:00")
                                        )
                                        date_str = parsed_date.strftime(
                                            "%Y-%m-%d %H:%M:%S"
                                        )
                                    except:
                                        # 如果解析失败，保持原格式
                                        pass
                            except:
                                # 如果所有解析都失败，保持原格式
                                pass

                        parsed_email_data = {
                            "from": msg.get("from", ""),
                            "subject": msg.get("subject", ""),
                            "date": date_str,
                            "body_preview": (
                                msg.get("body", "")[:200] if msg.get("body") else ""
                            ),
                        }
                        break

                # 初始化need_review标记
                need_review = False

                # 检查邮箱是否解析成功
                if not reply_email or not _is_valid_email(reply_email):
                    need_review = True

                # 确定平台和social_id - 从邮件内容中动态检测平台
                platform = None
                social_id = None
                parsed_social_link_data = {}

                # 首先尝试从thread_detail中获取（如果有的话）
                if tiktok_id:
                    platform = PlatformEnum.TIKTOK
                    social_id = tiktok_id
                    parsed_social_link_data["tiktok_id"] = tiktok_id
                elif tiktok_url:
                    match = re.search(r"tiktok\.com/@([^/?]+)", tiktok_url)
                    if match:
                        platform = PlatformEnum.TIKTOK
                        social_id = match.group(1)
                        parsed_social_link_data["tiktok_url"] = tiktok_url
                        parsed_social_link_data["extracted_id"] = social_id

                # 如果还没有找到social_id，从第一封邮件（发给KOL的营销邮件）中提取
                if not social_id and messages:
                    # 找到第一封发出的邮件（通常是营销邮件）
                    outbound_messages = [
                        msg for msg in messages if msg.get("is_self", False)
                    ]
                    if outbound_messages:
                        first_outbound = outbound_messages[0]
                        # 从邮件内容中提取社交媒体链接
                        # 检查邮件的各个字段中是否包含社交媒体链接
                        content_fields = [
                            first_outbound.get("subject", ""),
                            first_outbound.get("body", ""),
                            str(first_outbound),  # 整个邮件内容
                        ]

                        for content in content_fields:
                            if content:
                                # 定义各平台的链接匹配模式
                                platform_patterns = {
                                    PlatformEnum.TIKTOK: [
                                        r"tiktok\.com/@([^/?&\s>]+)",  # 标准TikTok链接
                                        r"www\.tiktok\.com/@([^/?&\s>]+)",  # 带www的链接
                                        r"track\.pstmrk\.it/[^/]+/www\.tiktok\.com/@([^/?&\s>]+)",  # 追踪链接
                                        r"click\.pstmrk\.it/[^/]+/www\.tiktok\.com/@([^/?&\s>]+)",  # 点击追踪链接
                                    ],
                                    PlatformEnum.INSTAGRAM: [
                                        r"instagram\.com/([^/?&\s>]+)",  # 标准Instagram链接
                                        r"www\.instagram\.com/([^/?&\s>]+)",  # 带www的链接
                                        r"track\.pstmrk\.it/[^/]+/www\.instagram\.com/([^/?&\s>]+)",  # 追踪链接
                                        r"click\.pstmrk\.it/[^/]+/www\.instagram\.com/([^/?&\s>]+)",  # 点击追踪链接
                                    ],
                                    PlatformEnum.YOUTUBE: [
                                        r"youtube\.com/@([^/?&\s>]+)",  # 标准YouTube链接
                                        r"www\.youtube\.com/@([^/?&\s>]+)",  # 带www的链接
                                        r"youtube\.com/channel/([^/?&\s>]+)",  # YouTube频道链接
                                        r"youtube\.com/c/([^/?&\s>]+)",  # YouTube自定义链接
                                        r"track\.pstmrk\.it/[^/]+/www\.youtube\.com/@([^/?&\s>]+)",  # 追踪链接
                                        r"click\.pstmrk\.it/[^/]+/www\.youtube\.com/@([^/?&\s>]+)",  # 点击追踪链接
                                    ],
                                }

                                # 按平台优先级检测（TikTok > Instagram > YouTube）
                                for (
                                    platform_enum,
                                    patterns,
                                ) in platform_patterns.items():
                                    for pattern in patterns:
                                        matches = re.findall(
                                            pattern, content, re.IGNORECASE
                                        )
                                        if matches:
                                            platform = platform_enum
                                            social_id = matches[0]
                                            parsed_social_link_data[
                                                "extracted_from_email"
                                            ] = content[:200]
                                            parsed_social_link_data[
                                                "pattern_matched"
                                            ] = pattern
                                            parsed_social_link_data["platform"] = (
                                                platform.value
                                            )
                                            break

                                    if social_id:
                                        break

                                if social_id:
                                    break

                # 检查社交媒体链接是否解析成功和格式是否有效
                if not social_id or not platform:
                    need_review = True
                    if not social_id:
                        parsed_social_link_data["error"] = "No social_id found"
                    if not platform:
                        parsed_social_link_data["error"] = "No platform detected"
                else:
                    # 构造完整的社交媒体链接进行验证
                    platform_url_map = {
                        PlatformEnum.TIKTOK: f"https://tiktok.com/@{social_id}",
                        PlatformEnum.INSTAGRAM: f"https://instagram.com/{social_id}",
                        PlatformEnum.YOUTUBE: f"https://youtube.com/@{social_id}",
                    }

                    full_link = platform_url_map.get(platform)
                    platform_name = platform.value.lower()

                    if full_link and not _is_valid_social_link(
                        full_link, platform_name
                    ):
                        need_review = True
                        parsed_social_link_data["error"] = (
                            f"Invalid {platform_name} link format"
                        )

                # 计算时间
                first_contact_date = None
                last_contact_date = None

                if messages:
                    dates = [msg.get("date") for msg in messages if msg.get("date")]
                    if dates:
                        first_contact_date = datetime.fromtimestamp(
                            min(dates) / 1000, tz=timezone.utc
                        )
                        last_contact_date = datetime.fromtimestamp(
                            max(dates) / 1000, tz=timezone.utc
                        )
                # 根据need_review状态决定如何处理KOL查找
                if need_review:
                    # 需要人工审核的情况：生成UUID作为kol_id，platform和social_id置空
                    kol_id = str(uuid.uuid4())
                    platform_for_record = None
                    social_id_for_record = None
                    nick_name = f"未找到社交媒体信息，需要审核"

                else:
                    # 正常情况：查找KOL
                    if social_id and platform:  # 确保social_id和platform都不为None
                        kol_obj = kol_crud.get_by_social_id_platform_project(
                            db,
                            social_id=social_id,
                            platform=platform,
                            project_code=project_code,
                        )
                    else:
                        kol_obj = None

                    if not kol_obj:
                        # 如果找不到KOL，也标记为需要审核
                        need_review = True
                        kol_id = str(uuid.uuid4())
                        platform_for_record = platform  # 保留检测到的平台信息
                        social_id_for_record = social_id  # 保留检测到的social_id
                        nick_name = f"未在数据库中找到对应的KOL，需要审核"
                        parsed_social_link_data["error"] = "KOL not found in database"
                    else:
                        # 找到KOL，使用KOL的信息更新候选人记录
                        kol_id = kol_obj.id
                        platform_for_record = kol_obj.platform  # 使用KOL表中的平台信息
                        social_id_for_record = (
                            kol_obj.social_id
                        )  # 使用KOL表中的social_id
                        nick_name = kol_obj.nick_name  # 使用KOL表中的昵称
                        # 标记不需要审核，因为已经找到对应的KOL
                        need_review = False

                # 构建候选人记录
                candidate_record = {
                    "platform": platform_for_record,
                    "kol_id": kol_id,
                    "social_id": social_id_for_record,
                    "nick_name": nick_name,
                    "project_code": project_code,
                    "reply_email_addr": reply_email,
                    "follow_up_status": None,  # 将根据label设置
                    "follow_up_note": f"从Gmail同步，标签: {label}",
                    "first_contact_date": first_contact_date,
                    "last_contact_date": last_contact_date,
                    "send_round": 0,  # 默认发送轮次为0
                    "latest_email_send_id": None,  # 暂时为空，后续可以关联
                    "thread_id": thread_id,
                    "tracker": tracker,
                    # TODO 解析出来的note格式为‘社交媒体URL: https://track.pstmrk.it/3s/www.tiktok.com/@blue4_j1/2eTs/9TC9AQ/AQ/19ead980-5d15-4d04-91e0-17fa43df5f89/1/syFASQvb1w>’ 末尾有一个多余的> 需要去除
                    "note": _clean_social_url_for_note(
                        tiktok_url, parsed_social_link_data
                    ),
                    "parsed_email": parsed_email_data,
                    "parsed_social_link": parsed_social_link_data,
                    "need_review": need_review,
                }

                # 根据label设置follow_up_status
                if "Processing" in label:
                    candidate_record["follow_up_status"] = FollowUpStatusEnum.PROCESSING
                elif "Completed" in label:
                    candidate_record["follow_up_status"] = FollowUpStatusEnum.COMPLETED
                elif "Drafting" in label:
                    candidate_record["follow_up_status"] = FollowUpStatusEnum.DRAFTING
                elif "Paid" in label:
                    candidate_record["follow_up_status"] = FollowUpStatusEnum.PAID
                elif "Not Target" in label or "NOT_TARGET" in label:
                    candidate_record["follow_up_status"] = FollowUpStatusEnum.NOT_TARGET
                elif "Off" in label or "OFF" in label:
                    candidate_record["follow_up_status"] = FollowUpStatusEnum.OFF
                else:
                    candidate_record["follow_up_status"] = FollowUpStatusEnum.PENDING

                candidate_records.append(candidate_record)
                print(
                    f"成功处理线程 {thread_id}: KOL ID={kol_id}, candidate_records={candidate_records}"
                )

            except Exception as e:
                print(f"处理线程详情时出错: {e}")
                continue

    finally:
        db.close()

    return candidate_records


class GmailAPI:
    base_url = "https://gmail.googleapis.com/gmail/v1/users/me"
    PAGE_SIZE = 500

    def __init__(self, access_token: str, email_domain: str):
        self.access_token = access_token
        self.headers = {"Authorization": f"Bearer {access_token}"}
        self.email_domain = email_domain

    def _request_with_retry(self, method, url, **kwargs):
        import time

        max_retries = 3
        retry_count = 0
        while retry_count < max_retries:
            try:
                time.sleep(0.2)
                response = requests.request(method, url, **kwargs)
                response.raise_for_status()
                return response
            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count < max_retries:
                    retry_delay = retry_count * 2
                    print(
                        f"请求 {url} 时遇到错误，{retry_delay}秒后重试第 {retry_count} 次: {e}"
                    )
                    time.sleep(retry_delay)
                else:
                    print(f"请求 {url} 失败(已重试{retry_count}次): {e}")
                    return None
        return None

    def _make_request(self, url: str, params: Optional[Dict] = None) -> Dict:
        response = self._request_with_retry(
            "get", url, headers=self.headers, params=params
        )
        return response.json() if response else {}

    def get_all_labels(self) -> list:
        print("正在获取所有Gmail标签...")
        url = f"{self.base_url}/labels"
        response = self._request_with_retry("get", url, headers=self.headers)
        if response:
            labels = response.json().get("labels", [])
            print(f"✅ 成功获取到 {len(labels)} 个标签")
            return [
                {"label_name": label["name"], "label_id": label["id"]}
                for label in labels
            ]
        return []

    def filter_relevant_labels(
        self, all_labels: List[Dict], labels: List[str], sublabels: List[str]
    ) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        all_relevant_labels = set(labels + sublabels)
        matched_labels = []
        matched_parent_labels = []
        matched_sublabels = []
        for label in all_labels:
            name = label.get("label_name")
            if name in all_relevant_labels:
                matched_labels.append(label)
                if name in labels:
                    matched_parent_labels.append(label)
                elif name in sublabels:
                    matched_sublabels.append(label)
        return matched_labels, matched_parent_labels, matched_sublabels

    def get_threads_by_labels(self, label_ids: List[str]) -> List[Dict]:
        url = f"{self.base_url}/threads"
        if isinstance(label_ids, str):
            label_ids = [label_ids]
        params = {"labelIds": ",".join(label_ids), "maxResults": self.PAGE_SIZE}
        all_threads = []
        page_token = None
        while True:
            if page_token:
                params["pageToken"] = page_token
            response = self._request_with_retry(
                "get", url, headers=self.headers, params=params
            )
            if not response:
                break
            data = response.json()
            threads = data.get("threads", [])
            all_threads.extend(threads)
            print(
                f"标签 '{label_ids}': 已获取 {len(threads)} 个线程，累计 {len(all_threads)} 个"
            )
            page_token = data.get("nextPageToken")
            if not page_token:
                break
        return all_threads

    def get_thread_details(self, thread_id: str) -> Dict:
        url = f"{self.base_url}/threads/{thread_id}"
        max_retries = 5
        backoff_factor = 1.5
        initial_delay = 1.0
        max_delay = 15.0
        import random
        import time

        for retry_count in range(max_retries):
            try:
                time.sleep(0.1)  # Proactive rate limiting
                response = requests.get(url, headers=self.headers)
                if response.status_code == 200:
                    return response.json()
                elif response.status_code in [403, 429]:
                    delay = min(
                        max_delay, initial_delay * (backoff_factor**retry_count)
                    )
                    jitter = random.uniform(0, delay * 0.1)
                    time.sleep(delay + jitter)
                    print(
                        f"线程ID {thread_id} 遇到 {response.status_code} 错误，重试 {retry_count + 1}/{max_retries}..."
                    )
                else:
                    print(
                        f"线程ID {thread_id} 请求失败: {response.status_code} {response.reason}"
                    )
                    return {}
            except requests.exceptions.RequestException as e:
                print(f"线程ID {thread_id} 请求异常: {e}")
        print(f"线程ID {thread_id} 已达到最大重试次数")
        return {}

    def _decode_base64_urlsafe(self, data: str) -> str:
        import base64

        if not data:
            return ""
        missing_padding = len(data) % 4
        if missing_padding:
            data += "=" * (4 - missing_padding)
        try:
            return base64.urlsafe_b64decode(data).decode("utf-8", errors="replace")
        except Exception as e:
            print(f"Base64 解码失败: {e}")
            return ""

    def _html_to_plaintext(self, html: str) -> str:
        from bs4 import BeautifulSoup

        try:
            soup = BeautifulSoup(html, "html.parser")
            text = soup.get_text(separator="\n")
            lines = text.splitlines()
            cleaned = [line.strip() for line in lines if line.strip()]
            return "\n".join(cleaned)
        except Exception as e:
            print(f"HTML 解析失败: {e}")
            return html

    def _extract_tiktok_info(self, text: str) -> Dict:
        import urllib.parse

        result = {
            "tiktok_url": None,
            "tiktok_id_from_url": None,
            "tiktok_id_from_text": None,
        }
        try:
            matches = re.findall(r"https://(?:\w+\.)?pstmrk\.it\S+", text)
            for url in matches:
                decoded = urllib.parse.unquote(url)
                if "tiktok.com" in decoded:
                    result["tiktok_url"] = "https://" + decoded.split("https://")[-1]
                    break
            if result["tiktok_url"]:
                match = re.search(r"tiktok\\.com/@([\\w\\.]+)", result["tiktok_url"])
                if match:
                    result["tiktok_id_from_url"] = match.group(1)
            name_match = re.search(
                r"^\\s*>?\\s*(?:Hey|Hi|Hello)\\s+([^,\\n]{1,50})\\s*,",
                text,
                re.IGNORECASE | re.MULTILINE,
            )
            if name_match:
                result["tiktok_id_from_text"] = name_match.group(1).strip()
        except Exception as e:
            print(f"提取TikTok信息失败: {e}")
        return result

    def analyze_thread_details(self, thread_details: Dict) -> Dict:
        if not thread_details or "messages" not in thread_details:
            return {}
        import email.utils

        thread_id = thread_details.get("id", "")
        messages = sorted(
            thread_details.get("messages", []),
            key=lambda x: int(x.get("internalDate", 0)),
        )
        processed_emails = []
        tiktok_url, tiktok_id = None, None
        for message in messages:
            headers = message.get("payload", {}).get("headers", [])
            email_data = {
                h["name"].lower().replace("-", "_"): h["value"] for h in headers
            }
            if "date" in email_data:
                try:
                    parsed_date = email.utils.parsedate_to_datetime(email_data["date"])
                    email_data["date"] = int(parsed_date.timestamp() * 1000)
                except Exception as e:
                    print(f"日期解析失败: {e}")
                    email_data["date"] = None
            email_data["labelids"] = message.get("labelIds", [])
            for part in message.get("payload", {}).get("parts", []):
                body_data = part.get("body", {}).get("data", "")
                if not body_data:
                    continue
                decoded_str = self._decode_base64_urlsafe(body_data)
                if part.get("mimeType") == "text/html":
                    decoded_str = self._html_to_plaintext(decoded_str)
                if not tiktok_url:
                    tiktok_info = self._extract_tiktok_info(decoded_str)
                    if tiktok_info["tiktok_url"]:
                        tiktok_url = tiktok_info["tiktok_url"]
                    if tiktok_info["tiktok_id_from_url"]:
                        tiktok_id = tiktok_info["tiktok_id_from_url"]
                    elif not tiktok_id and tiktok_info["tiktok_id_from_text"]:
                        tiktok_id = tiktok_info["tiktok_id_from_text"]
            email_data["is_self"] = self.email_domain in email_data.get("from", "")
            processed_emails.append(email_data)
        return {
            "thread_id": thread_id,
            "messages": processed_emails,
            "tiktok_url": tiktok_url,
            "tiktok_id": tiktok_id,
        }

    def map_label_ids(
        self, follow_up_names: List[str], all_labels: List[Dict]
    ) -> Dict[str, str]:
        label_id_to_follow_up = {}
        for follow_up in follow_up_names:
            for label in all_labels:
                if follow_up in label.get("label_name", ""):
                    label_id_to_follow_up[label.get("label_id")] = follow_up
                    break
        return label_id_to_follow_up

    def process_labels_and_get_unique_threads(self, matched_labels: List[Dict]) -> Dict:
        print(f"开始处理 {len(matched_labels)} 个标签...")
        label_to_threads = {
            label["label_name"]: self.get_threads_by_labels(label["label_id"])
            for label in matched_labels
        }
        # child_labels = {name for name in label_to_threads if '/' in name}
        # parent_labels = {name for name in label_to_threads if '/' not in name}
        # parent_to_exclude_ids = {}
        # for child in child_labels:
        #     parent = child.split('/')[0]
        #     if parent in parent_labels:
        #         parent_to_exclude_ids.setdefault(parent, set()).update(t['id'] for t in label_to_threads[child])
        label_to_unique_ids = {}
        for name, threads in label_to_threads.items():
            all_ids = {t["id"] for t in threads}
            # if name in parent_to_exclude_ids:
            #     unique_ids = all_ids - parent_to_exclude_ids[name]
            #     print(f"主标签 {name} 中排除了 {len(all_ids) - len(unique_ids)} 个线程")
            #     label_to_unique_ids[name] = unique_ids
            # else:
            label_to_unique_ids[name] = all_ids
        return label_to_unique_ids

    def fetch_thread_details(
        self, label_to_unique_ids: Dict, max_workers: int = 20
    ) -> List[Dict]:
        # 暂时写死OOG120用于测试
        label_to_unique_ids["OOG120"] = set(list(label_to_unique_ids["OOG120"])[:5])
        all_thread_details = []
        total_threads = sum(len(ids) for ids in label_to_unique_ids.values())
        print(f"\n开始获取 {total_threads} 个线程的详细信息...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_thread = {
                executor.submit(self.get_thread_details, thread_id): (
                    thread_id,
                    label_name,
                )
                for label_name, thread_ids in label_to_unique_ids.items()
                for thread_id in thread_ids
            }

            completed_count = 0
            for future in concurrent.futures.as_completed(future_to_thread):
                completed_count += 1
                thread_id, label_name = future_to_thread[future]
                print(
                    f"[线程获取进度 {completed_count}/{total_threads}] 开始处理线程ID: {thread_id}"
                )
                try:
                    thread_details = future.result()
                    if thread_details:
                        analysis = self.analyze_thread_details(thread_details)
                        if analysis:
                            analysis["label"] = label_name
                            all_thread_details.append(analysis)
                            print(
                                f"✅ [线程获取进度 {completed_count}/{total_threads}] 成功处理线程ID: {thread_id}"
                            )
                        else:
                            print(
                                f"⚠️ [线程获取进度 {completed_count}/{total_threads}] 线程ID: {thread_id} 分析结果为空"
                            )
                    else:
                        print(
                            f"⚠️ [线程获取进度 {completed_count}/{total_threads}] 线程ID: {thread_id} 获取详情失败"
                        )
                except Exception as e:
                    print(
                        f"❌ [线程获取进度 {completed_count}/{total_threads}] 处理线程 {thread_id} 时发生错误: {e}"
                    )
        print(f"\n共获取到 {len(all_thread_details)} 个线程详情")
        with open(
            "/Users/<USER>/prjs/work/kol/scripts/all_thread_details.txt", "w"
        ) as f:
            f.write(str(all_thread_details))
        return all_thread_details

    def get_project_and_sublabels(
        self, all_labels: List[Dict], project: str
    ) -> Dict[str, List[Dict]]:
        project_label = []
        sub_labels = []
        for label in all_labels:
            label_name = label.get("label_name", "")
            if label_name == project:
                project_label.append(label)
            elif label_name.startswith(f"{project}/"):
                sub_labels.append(label)
        return {"project_label": project_label, "sub_labels": sub_labels}


def get_project_config(project_code):
    db = SessionLocal()
    try:
        project_obj = project_crud.get_by_code(db, code=project_code)
    finally:
        db.close()
    file_path = project_obj.gmail_token_path
    with open(file_path, "rb") as token:
        creds = pickle.load(token)
    if creds and creds.expired and creds.refresh_token:
        creds.refresh(Request())
    project_cfg = {
        "email_domain": project_obj.email_domain,
        "email_labels": project_obj.email_label,
        "email_sublabels": project_obj.email_sublabel,
        "gmail_token": creds.token,
        "project_code": project_code,
        "tracker": project_obj.tracker,
    }
    # project_obj 转为project_info dict返回
    return project_cfg


def upsert_candidates_to_db(candidate_records: List[Dict]) -> Dict[str, int]:
    """
    将候选人记录批量upsert到数据库

    Args:
        candidate_records: 候选人记录列表

    Returns:
        Dict包含操作统计信息: {"created": int, "updated": int, "errors": int}
    """
    from app.crud.candidate import candidate as candidate_crud
    from app.schemas.candidate import CandidateCreate, CandidateUpdate

    db = SessionLocal()
    created_count = 0
    updated_count = 0
    error_count = 0

    try:
        for record in candidate_records:
            try:
                thread_id = record.get("thread_id")
                project_code = record.get("project_code")

                if not thread_id or not project_code:
                    print(f"缺少必要字段 thread_id 或 project_code: {record}")
                    error_count += 1
                    continue

                # 检查是否已存在相同的候选人（根据thread_id）
                existing = candidate_crud.get_by_thread_id(db, thread_id=thread_id)

                if existing:
                    # 更新现有记录
                    update_data = {
                        k: v
                        for k, v in record.items()
                        if k not in ["thread_id", "project_code"]
                    }  # 排除主键字段
                    candidate_update = CandidateUpdate(**update_data)
                    candidate_crud.update(db, db_obj=existing, obj_in=candidate_update)
                    updated_count += 1

                    # 检查是否更新了KOL信息
                    kol_info = ""
                    if record.get("kol_id") and record.get("kol_id") != existing.kol_id:
                        kol_info = (
                            f", 更新KOL ID: {existing.kol_id} -> {record.get('kol_id')}"
                        )
                    if (
                        record.get("platform")
                        and record.get("platform") != existing.platform
                    ):
                        kol_info += f", 更新平台: {existing.platform} -> {record.get('platform')}"

                    print(
                        f"成功更新候选人: thread_id={thread_id}, KOL ID={record.get('kol_id')}{kol_info}"
                    )
                else:
                    # 创建新记录
                    candidate_create = CandidateCreate(**record)
                    candidate_crud.create(db, obj_in=candidate_create)
                    created_count += 1
                    print(
                        f"成功创建候选人: thread_id={thread_id}, KOL ID={record.get('kol_id')}, 平台={record.get('platform')}"
                    )

            except Exception as e:
                error_count += 1
                print(f"处理候选人记录失败: {e}, 记录: {record}")
                continue

        db.commit()
        print(
            f"Upsert完成: 创建 {created_count} 条，更新 {updated_count} 条，失败 {error_count} 条"
        )

        return {
            "created": created_count,
            "updated": updated_count,
            "errors": error_count,
        }

    except Exception as e:
        db.rollback()
        print(f"数据库操作过程中发生错误: {e}")
        return {"created": 0, "updated": 0, "errors": len(candidate_records)}
    finally:
        db.close()


def handler(project_code):
    """处理Gmail数据并入库到candidates表"""
    project_cfg = get_project_config(project_code)

    gmail_client = GmailAPI(
        project_cfg.get("gmail_token"), project_cfg.get("email_domain")
    )
    gmail_data = get_gmail_data(project_cfg, project_code, gmail_client)

    if not gmail_data:
        print(f"项目 {project_code} 没有获取到Gmail数据")
        return

    print(f"获取到 {len(gmail_data)} 条候选人记录，开始入库...")

    # 调用upsert方法入库
    result = upsert_candidates_to_db(gmail_data)

    print(f"处理完成: {result}")
    return result


if __name__ == "__main__":
    handler("OOG120")
