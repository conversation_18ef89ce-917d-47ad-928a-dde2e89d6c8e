"""
同步绩效数据服务模块
使用策略模式+工厂模式重构的绩效数据获取服务
支持 TikTok、Instagram、YouTube 平台的数据同步
"""

import logging
import os
import re
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Union

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from app.config import settings

# 设置日志
logger = logging.getLogger(__name__)


def convert_followers_to_k(followers: int) -> float:
    """将粉丝数转换为K为单位"""
    return round(followers / 1000, 1) if followers is not None else 0


def get_platform_from_url(url: str) -> Optional[str]:
    """从URL中识别平台类型"""
    if not url:
        return None

    url_lower = url.lower()

    if "tiktok.com" in url_lower:
        return "tiktok"
    elif "instagram.com" in url_lower:
        return "instagram"
    elif "youtube.com" in url_lower or "youtu.be" in url_lower:
        return "youtube"

    return None


# ==================== 策略模式抽象基类 ====================
class PerformanceDataProcessor(ABC):
    """绩效数据处理器抽象基类（策略模式）"""

    @abstractmethod
    def get_video_stats(self, video_url: str) -> Optional[Dict]:
        """获取视频统计数据"""

    @abstractmethod
    def get_request_params(self, video_url: str) -> Optional[Dict]:
        """获取异步请求参数（用于并发处理）"""

    @abstractmethod
    def parse_response(self, response_data: Dict, video_url: str) -> Optional[Dict]:
        """解析API响应数据"""

    @abstractmethod
    def get_platform_name(self) -> str:
        """获取平台名称"""


# ==================== TikHub API 基础类 ====================
class TikHubAPIClient:
    """TikHub API 客户端基础类"""

    def __init__(self):
        self.base_url = os.environ.get("TIKHUB_API_BASE_URL", "https://api.tikhub.io")
        self.api_key = settings.TIKHUB_TOKEN
        self.max_retries = int(os.environ.get("TIKHUB_MAX_RETRIES", "3"))
        self.base_delay = int(os.environ.get("TIKHUB_BASE_DELAY", "1"))

        if not self.api_key:
            logger.warning("TikHub API密钥未配置，请设置TIKHUB_API_KEY环境变量")

        self.session = self._create_retry_session()

    def _create_retry_session(self) -> requests.Session:
        """创建带重试机制的Session"""
        session = requests.Session()
        retry = Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["GET", "POST"],
        )
        adapter = HTTPAdapter(max_retries=retry)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def _make_request(
        self, endpoint: str, params: Optional[Dict] = None
    ) -> Optional[Dict]:
        """发送HTTP请求（带重试机制）"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

        for attempt in range(self.max_retries + 1):
            try:
                response = self.session.get(
                    url, params=params, headers=headers, timeout=30
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    raise Exception(f"HTTP {response.status_code}: {response.text}")

            except Exception as e:
                if attempt < self.max_retries:
                    wait_time = self.base_delay * (2**attempt)
                    logger.warning(
                        f"TikHub API请求失败，第{attempt + 1}次重试（{wait_time}秒后）: {str(e)}"
                    )
                    time.sleep(wait_time)
                else:
                    logger.error(
                        f"TikHub API请求失败（已重试{self.max_retries}次）: {str(e)}"
                    )
                    return None


# ==================== TikTok处理器模块（策略模式实现）====================
class TikTokProcessor(TikHubAPIClient, PerformanceDataProcessor):
    """TikTok数据处理类（策略模式实现）"""

    def __init__(self):
        super().__init__()

    def get_platform_name(self) -> str:
        """获取平台名称"""
        return "TIKTOK"

    def get_video_stats(self, video_url: str) -> Optional[Dict]:
        """获取TikTok视频统计数据"""
        logger.info(f"开始获取TikTok视频数据: {video_url}")

        # 使用TikHub API获取视频数据
        endpoint = "/api/v1/tiktok/app/v3/fetch_one_video_by_share_url"
        params = {"share_url": video_url}

        response = self._make_request(endpoint, params)

        if not response or response.get("code") != 200:
            logger.error(f"TikTok API调用失败: {response}")
            return None

        try:
            # 解析TikHub API响应数据
            data = response.get("data", {})

            # 根据新的API结构，数据在 aweme_details 中
            aweme_details = data.get("aweme_details", [])

            if not aweme_details:
                logger.error("TikTok视频数据为空 - aweme_details")
                # 尝试旧的数据结构作为备选
                aweme_list = data.get("aweme_list", [])
                if not aweme_list:
                    logger.error("TikTok视频数据为空 - aweme_list")
                    return None
                video_data = aweme_list[0]
            else:
                video_data = aweme_details[0]  # 获取第一个视频数据

            # 提取统计数据
            statistics = video_data.get("statistics", {})
            author = video_data.get("author", {})

            # 获取视频ID和创建时间
            video_id = video_data.get("aweme_id", "") or statistics.get("aweme_id", "")
            create_time = video_data.get("create_time", 0)

            # 获取作者粉丝数
            follower_count = author.get("follower_count", 0)
            follower_count_k = convert_followers_to_k(follower_count)

            # 构建标准化的统计数据
            stats_data = {
                "video_id": video_id,
                "create_time": (
                    datetime.fromtimestamp(create_time).strftime("%Y/%m/%d")
                    if create_time
                    else ""
                ),
                "platform": "tiktok",
                "stats": {
                    "play_count": statistics.get("play_count", 0),
                    "digg_count": statistics.get("digg_count", 0),
                    "comment_count": statistics.get("comment_count", 0),
                    "share_count": statistics.get("share_count", 0),
                    "collect_count": statistics.get("collect_count", 0),
                    "follower_count": follower_count_k,
                },
            }

            logger.info(
                f"获取TikTok视频数据成功: ID={stats_data['video_id']}, 播放量={stats_data['stats']['play_count']}"
            )
            return stats_data

        except Exception as e:
            logger.error(f"解析TikTok数据失败: {str(e)}")
            return None

    def get_request_params(self, video_url: str) -> Optional[Dict]:
        """获取TikTok异步请求参数"""
        endpoint = "/api/v1/tiktok/app/v3/fetch_one_video_by_share_url"
        params = {"share_url": video_url}

        return {
            "url": f"{self.base_url}{endpoint}",
            "method": "GET",
            "params": params,
            "headers": {
                "accept": "application/json",
                "Authorization": f"Bearer {self.api_key}",
            },
            "timeout": 30,
            "video_url": video_url,  # 保存原始URL用于后续处理
        }

    def parse_response(self, response_data: Dict, video_url: str) -> Optional[Dict]:
        """解析TikTok API响应数据"""
        if not response_data or response_data.get("status_code") != 200:
            logger.error(f"TikTok API响应失败: {response_data}")
            return None

        try:
            json_data = response_data.get("json")
            if not json_data or json_data.get("code") != 200:
                logger.error(f"TikTok API调用失败: {json_data}")
                return None

            # 解析数据结构
            data = json_data.get("data", {})
            aweme_details = data.get("aweme_details", [])

            if not aweme_details:
                aweme_list = data.get("aweme_list", [])
                if not aweme_list:
                    logger.error("TikTok视频数据为空")
                    return None
                video_data = aweme_list[0]
            else:
                video_data = aweme_details[0]

            # 提取统计数据
            statistics = video_data.get("statistics", {})
            author = video_data.get("author", {})

            video_id = video_data.get("aweme_id", "") or statistics.get("aweme_id", "")
            create_time = video_data.get("create_time", 0)

            follower_count = author.get("follower_count", 0)
            follower_count_k = convert_followers_to_k(follower_count)

            return {
                "video_id": video_id,
                "create_time": (
                    datetime.fromtimestamp(create_time).strftime("%Y/%m/%d")
                    if create_time
                    else ""
                ),
                "platform": "tiktok",
                "stats": {
                    "play_count": statistics.get("play_count", 0),
                    "digg_count": statistics.get("digg_count", 0),
                    "comment_count": statistics.get("comment_count", 0),
                    "share_count": statistics.get("share_count", 0),
                    "collect_count": statistics.get("collect_count", 0),
                    "follower_count": follower_count_k,
                },
            }

        except Exception as e:
            logger.error(f"解析TikTok响应数据失败: {str(e)}")
            return None


# ==================== Instagram处理器模块（TikHub API版本）====================
def extract_instagram_shortcode(url: str) -> str:
    """从Instagram URL中提取视频shortcode"""
    patterns = [
        r"instagram\.com/p/([^/]+)",
        r"instagram\.com/reel/([^/]+)",
    ]

    for pattern in patterns:
        if match := re.search(pattern, url):
            return match.group(1)

    raise ValueError(f"无法从URL中提取shortcode: {url}")


class InstagramProcessor(TikHubAPIClient, PerformanceDataProcessor):
    """Instagram数据处理类（策略模式实现）"""

    def __init__(self):
        super().__init__()

    def get_platform_name(self) -> str:
        """获取平台名称"""
        return "INSTAGRAM"

    def get_video_stats(self, video_url: str) -> Optional[Dict]:
        """获取Instagram视频统计数据"""
        logger.info(f"开始获取Instagram视频数据: {video_url}")

        try:
            # 从URL提取shortcode
            shortcode = extract_instagram_shortcode(video_url)
        except ValueError as e:
            logger.error(f"无效的Instagram URL: {str(e)}")
            return None

        # 使用TikHub API获取Instagram数据
        endpoint = "/api/v1/instagram/web_app/fetch_post_details_by_code"
        params = {"shortcode": shortcode}

        response = self._make_request(endpoint, params)

        if not response or response.get("code") != 200:
            logger.error(f"Instagram API调用失败: {response}")
            return None

        try:
            # 解析TikHub API响应数据
            data = response.get("data", {})
            post_data = data.get("data", {})

            if not post_data:
                logger.error("Instagram视频数据为空")
                return None

            # 提取统计数据 - 根据实际数据结构调整
            metrics = post_data.get("metrics", {})

            # 获取视频ID和创建时间
            video_id = post_data.get("id", shortcode)
            device_timestamp = post_data.get("device_timestamp", 0)

            # 获取作者粉丝数 - 从metrics中获取
            follower_count = metrics.get("user_follower_count", 0) or 0
            follower_count_k = convert_followers_to_k(follower_count)

            # 构建标准化的统计数据 - 根据实际JSON结构调整
            stats_data = {
                "video_id": video_id,
                "create_time": (
                    datetime.fromtimestamp(device_timestamp / 1000).strftime("%Y/%m/%d")
                    if device_timestamp
                    else ""
                ),
                "platform": "instagram",
                "stats": {
                    "play_count": metrics.get("play_count", 0)
                    or post_data.get("ig_play_count", 0),
                    "digg_count": metrics.get("like_count", 0),
                    "comment_count": metrics.get("comment_count", 0),
                    "share_count": metrics.get("share_count", 0)
                    or 0,  # Instagram通常不提供分享数
                    "collect_count": metrics.get("save_count", 0) or 0,
                    "follower_count": follower_count_k,
                },
            }

            logger.info(f"获取Instagram视频数据成功: ID={stats_data['video_id']}")
            return stats_data

        except Exception as e:
            logger.error(f"解析Instagram数据失败: {str(e)}")
            return None

    def get_request_params(self, video_url: str) -> Optional[Dict]:
        """获取Instagram异步请求参数"""
        try:
            shortcode = extract_instagram_shortcode(video_url)
        except ValueError as e:
            logger.error(f"无效的Instagram URL: {str(e)}")
            return None

        endpoint = "/api/v1/instagram/web_app/fetch_post_details_by_code"
        params = {"shortcode": shortcode}

        return {
            "url": f"{self.base_url}{endpoint}",
            "method": "GET",
            "params": params,
            "headers": {
                "accept": "application/json",
                "Authorization": f"Bearer {self.api_key}",
            },
            "timeout": 30,
            "video_url": video_url,
        }

    def parse_response(self, response_data: Dict, video_url: str) -> Optional[Dict]:
        """解析Instagram API响应数据"""
        if not response_data or response_data.get("status_code") != 200:
            logger.error(f"Instagram API响应失败: {response_data}")
            return None

        try:
            json_data = response_data.get("json")
            if not json_data or json_data.get("code") != 200:
                logger.error(f"Instagram API调用失败: {json_data}")
                return None

            data = json_data.get("data", {})
            post_data = data.get("data", {})

            if not post_data:
                logger.error("Instagram视频数据为空")
                return None

            metrics = post_data.get("metrics", {})
            shortcode = extract_instagram_shortcode(video_url)
            video_id = post_data.get("id", shortcode)
            device_timestamp = post_data.get("device_timestamp", 0)

            follower_count = metrics.get("user_follower_count", 0) or 0
            follower_count_k = convert_followers_to_k(follower_count)

            return {
                "video_id": video_id,
                "create_time": (
                    datetime.fromtimestamp(device_timestamp / 1000).strftime("%Y/%m/%d")
                    if device_timestamp
                    else ""
                ),
                "platform": "instagram",
                "stats": {
                    "play_count": metrics.get("play_count", 0)
                    or post_data.get("ig_play_count", 0),
                    "digg_count": metrics.get("like_count", 0),
                    "comment_count": metrics.get("comment_count", 0),
                    "share_count": metrics.get("share_count", 0) or 0,
                    "collect_count": metrics.get("save_count", 0) or 0,
                    "follower_count": follower_count_k,
                },
            }

        except Exception as e:
            logger.error(f"解析Instagram响应数据失败: {str(e)}")
            return None


# ==================== YouTube处理器模块（TikHub API版本）====================
def extract_youtube_video_id(url: str) -> str:
    """从YouTube URL中提取视频ID"""
    patterns = [
        r"(?:www\.|m\.)?youtube\.com/watch\?v=([^&]+)",  # 支持常规YouTube视频链接
        r"youtu\.be/([^?]+)",  # 支持YouTube短链接
        r"(?:www\.|m\.)?youtube\.com/embed/([^?]+)",  # 支持YouTube嵌入链接
        r"(?:www\.|m\.)?youtube\.com/v/([^?]+)",  # 支持YouTube旧版链接
        r"(?:www\.|m\.)?youtube\.com/shorts/([^?]+)",  # 支持YouTube Shorts链接（包含www和移动版）
    ]

    for pattern in patterns:
        if match := re.search(pattern, url):
            return match.group(1)

    raise ValueError(f"无法从URL中提取YouTube视频ID: {url}")


class YouTubeProcessor(TikHubAPIClient, PerformanceDataProcessor):
    """YouTube数据处理类（策略模式实现）"""

    def __init__(self):
        super().__init__()

    def get_platform_name(self) -> str:
        """获取平台名称"""
        return "YOUTUBE"

    def get_video_stats(self, video_url: str) -> Optional[Dict]:
        """获取YouTube视频统计数据"""
        logger.info(f"开始获取YouTube视频数据: {video_url}")

        try:
            # 从URL提取视频ID
            video_id = extract_youtube_video_id(video_url)
        except ValueError as e:
            logger.error(f"无效的YouTube URL: {str(e)}")
            return None

        # 使用TikHub API获取YouTube数据
        endpoint = "/api/v1/youtube/web/get_video_info"
        params = {
            "video_id": video_id,
            "url_access": "normal",
            "lang": "zh-CN",
            "videos": "false",
            "audios": "false",
        }

        response = self._make_request(endpoint, params)

        if not response or response.get("code") != 200:
            logger.error(f"YouTube API调用失败: {response}")
            return None

        try:
            # 解析TikHub API响应数据
            data = response.get("data", {})

            if not data:
                logger.error("YouTube视频数据为空")
                return None

            # 提取统计数据 - 根据实际数据结构调整
            view_count = data.get("viewCount", 0)
            like_count = data.get("likeCount", 0)
            comment_count_text = data.get("commentCountText", "0")

            # 解析评论数（可能是字符串格式）
            try:
                if isinstance(comment_count_text, str):
                    comment_count = int(comment_count_text.replace(",", ""))
                else:
                    comment_count = int(comment_count_text) if comment_count_text else 0
            except (ValueError, AttributeError):
                comment_count = 0

            # 获取发布时间
            published_time = data.get("publishedTime", "")

            # 获取频道信息和订阅者数
            channel = data.get("channel", {})
            # 从subscriberCountText中解析订阅者数
            subscriber_count_text = channel.get("subscriberCountText", "0")
            subscriber_count = 0
            if subscriber_count_text:
                try:
                    # 处理类似 "14.2万位订阅者" 的格式
                    if "万" in subscriber_count_text:
                        num_str = subscriber_count_text.replace(
                            "万位订阅者", ""
                        ).replace("万", "")
                        subscriber_count = int(float(num_str) * 10000)
                    elif "位订阅者" in subscriber_count_text:
                        num_str = subscriber_count_text.replace("位订阅者", "").replace(
                            ",", ""
                        )
                        subscriber_count = int(num_str)
                except (ValueError, AttributeError):
                    subscriber_count = 0

            subscriber_count_k = convert_followers_to_k(subscriber_count)

            # 构建标准化的统计数据
            stats_data = {
                "video_id": video_id,
                "create_time": (
                    published_time.split("T")[0].replace("-", "/")
                    if published_time
                    else ""
                ),
                "platform": "youtube",
                "stats": {
                    "play_count": view_count,
                    "digg_count": like_count,
                    "comment_count": comment_count,
                    "share_count": 0,  # YouTube API通常不提供分享数
                    "collect_count": 0,  # YouTube没有收藏概念
                    "follower_count": subscriber_count_k,
                },
            }

            logger.info(f"获取YouTube视频数据成功: ID={stats_data['video_id']}")
            return stats_data

        except Exception as e:
            logger.error(f"解析YouTube数据失败: {str(e)}")
            return None

    def get_request_params(self, video_url: str) -> Optional[Dict]:
        """获取YouTube异步请求参数"""
        try:
            video_id = extract_youtube_video_id(video_url)
        except ValueError as e:
            logger.error(f"无效的YouTube URL: {str(e)}")
            return None

        endpoint = "/api/v1/youtube/web/get_video_info"
        params = {
            "video_id": video_id,
            "url_access": "normal",
            "lang": "zh-CN",
            "videos": "false",
            "audios": "false",
        }

        return {
            "url": f"{self.base_url}{endpoint}",
            "method": "GET",
            "params": params,
            "headers": {
                "accept": "application/json",
                "Authorization": f"Bearer {self.api_key}",
            },
            "timeout": 30,
            "video_url": video_url,
        }

    def parse_response(self, response_data: Dict, video_url: str) -> Optional[Dict]:
        """解析YouTube API响应数据"""
        if not response_data or response_data.get("status_code") != 200:
            logger.error(f"YouTube API响应失败: {response_data}")
            return None

        try:
            json_data = response_data.get("json")
            if not json_data or json_data.get("code") != 200:
                logger.error(f"YouTube API调用失败: {json_data}")
                return None

            data = json_data.get("data", {})
            if not data:
                logger.error("YouTube视频数据为空")
                return None

            video_id = extract_youtube_video_id(video_url)
            view_count = data.get("viewCount", 0)
            like_count = data.get("likeCount", 0)
            comment_count_text = data.get("commentCountText", "0")

            # 解析评论数
            try:
                if isinstance(comment_count_text, str):
                    comment_count = int(comment_count_text.replace(",", ""))
                else:
                    comment_count = int(comment_count_text) if comment_count_text else 0
            except (ValueError, AttributeError):
                comment_count = 0

            published_time = data.get("publishedTime", "")

            # 获取订阅者数
            channel = data.get("channel", {})
            subscriber_count_text = channel.get("subscriberCountText", "0")
            subscriber_count = 0
            if subscriber_count_text:
                try:
                    if "万" in subscriber_count_text:
                        num_str = subscriber_count_text.replace(
                            "万位订阅者", ""
                        ).replace("万", "")
                        subscriber_count = int(float(num_str) * 10000)
                    elif "位订阅者" in subscriber_count_text:
                        num_str = subscriber_count_text.replace("位订阅者", "").replace(
                            ",", ""
                        )
                        subscriber_count = int(num_str)
                except (ValueError, AttributeError):
                    subscriber_count = 0

            subscriber_count_k = convert_followers_to_k(subscriber_count)

            return {
                "video_id": video_id,
                "create_time": (
                    published_time.split("T")[0].replace("-", "/")
                    if published_time
                    else ""
                ),
                "platform": "youtube",
                "stats": {
                    "play_count": view_count,
                    "digg_count": like_count,
                    "comment_count": comment_count,
                    "share_count": 0,
                    "collect_count": 0,
                    "follower_count": subscriber_count_k,
                },
            }

        except Exception as e:
            logger.error(f"解析YouTube响应数据失败: {str(e)}")
            return None


# ==================== 工厂模式 ====================
class PerformanceProcessorFactory:
    """绩效数据处理器工厂类"""

    _processors = {
        "tiktok": TikTokProcessor,
        "instagram": InstagramProcessor,
        "youtube": YouTubeProcessor,
    }

    @classmethod
    def create_processor(cls, platform: str) -> Optional[PerformanceDataProcessor]:
        """根据平台类型创建对应的处理器"""
        platform_lower = platform.lower()
        processor_class = cls._processors.get(platform_lower)

        if processor_class:
            return processor_class()
        else:
            logger.error(f"不支持的平台: {platform}")
            return None

    @classmethod
    def get_supported_platforms(cls) -> List[str]:
        """获取支持的平台列表"""
        return list(cls._processors.keys())


# ==================== 统一的绩效数据获取接口 ====================
def get_performance_data(
    post_link: str, project_code: str, kol_id: int, social_id: str = ""
) -> Optional[Dict]:
    """
    统一的绩效数据获取方法（策略模式+工厂模式）

    Args:
        post_link: 帖子链接
        project_code: 项目编码
        kol_id: KOL ID
        social_id: KOL的社交媒体ID

    Returns:
        解析后的绩效数据，包含平台信息和统计数据
    """
    logger.info(
        f"开始获取绩效数据: post_link={post_link}, project_code={project_code}, kol_id={kol_id}"
    )

    try:
        # 识别平台类型
        platform = get_platform_from_url(post_link)
        if not platform:
            logger.error(f"无法识别平台类型: {post_link}")
            return None

        # 使用工厂模式创建处理器
        processor = PerformanceProcessorFactory.create_processor(platform)
        if not processor:
            return None

        # 获取视频统计数据
        stats_data = processor.get_video_stats(post_link)
        if not stats_data:
            logger.error(f"获取视频统计数据失败: {post_link}")
            return None

        # 构建标准化的绩效数据
        performance_data = _build_performance_data(
            stats_data, platform, project_code, kol_id, post_link, social_id
        )

        logger.info(
            f"成功解析绩效数据: platform={performance_data['platform']}, views={performance_data['views_total']}"
        )
        return performance_data

    except Exception as e:
        logger.error(f"获取绩效数据异常: {str(e)}")
        return None


def _build_performance_data(
    stats_data: Dict,
    platform: str,
    project_code: str,
    kol_id: int,
    post_link: str,
    social_id: str = "",
) -> Dict:
    """构建标准化的绩效数据（只包含播放量相关数据和 post_link）"""
    # 使用传入的值构建绩效数据
    performance_data = {
        "platform": platform.upper() if platform else "TIKTOK",  # 使用传入的平台
        "kol_id": kol_id if kol_id else 1,  # 使用传入的 kol_id
        "project_code": (
            project_code if project_code else "DEFAULT"
        ),  # 使用传入的 project_code
        "views_total": stats_data["stats"].get("play_count", 0),
        "likes_total": stats_data["stats"].get("digg_count", 0),
        "comments_total": stats_data["stats"].get("comment_count", 0),
        "shares_total": stats_data["stats"].get("share_count", 0),
        "post_link": post_link,
    }

    return performance_data


# ==================== 向后兼容的方法 ====================
def get_tk_pf_data(post_link: str, project_code: str, kol_id: int) -> Optional[Dict]:
    """
    获取TikTok绩效数据（向后兼容方法）

    Args:
        post_link: 帖子链接
        project_code: 项目编码
        kol_id: KOL ID

    Returns:
        解析后的绩效数据，包含平台信息和统计数据
    """
    return get_performance_data(post_link, project_code, kol_id)


def get_ins_pf_data(post_link: str, project_code: str, kol_id: int) -> Optional[Dict]:
    """
    获取Instagram绩效数据（向后兼容方法）

    Args:
        post_link: Instagram帖子链接
        project_code: 项目编码
        kol_id: KOL ID

    Returns:
        解析后的Instagram绩效数据
    """
    return get_performance_data(post_link, project_code, kol_id)


def get_yt_pf_data(post_link: str, project_code: str, kol_id: int) -> Optional[Dict]:
    """
    获取YouTube绩效数据（向后兼容方法）

    Args:
        post_link: YouTube视频链接
        project_code: 项目编码
        kol_id: KOL ID

    Returns:
        解析后的YouTube绩效数据
    """
    return get_performance_data(post_link, project_code, kol_id)


def get_performance_data_batch(
    post_links_data: List[Dict[str, Union[str, int]]], max_concurrent: int = 5
) -> List[Dict]:
    """
    批量获取绩效数据（直接使用 AsyncHttpRequestStep）

    Args:
        post_links_data: 包含帖子信息的列表
        max_concurrent: 最大并发数

    Returns:
        绩效数据列表
    """
    if not post_links_data:
        return []

    logger.info(
        f"开始批量获取 {len(post_links_data)} 个绩效数据，最大并发数: {max_concurrent}"
    )

    # 按平台分组并生成请求参数
    all_requests = []
    request_metadata = []

    for item in post_links_data:
        post_link = str(item.get("post_link", ""))
        platform = get_platform_from_url(post_link)
        if not platform:
            logger.warning(f"无法识别平台类型，跳过: {post_link}")
            continue

        processor = PerformanceProcessorFactory.create_processor(platform)
        if not processor:
            continue

        request_params = processor.get_request_params(post_link)
        if request_params:
            all_requests.append(request_params)
            request_metadata.append(
                {"platform": platform, "processor": processor, "original_data": item}
            )

    if not all_requests:
        logger.warning("没有有效的请求参数")
        return []

    # 直接使用 AsyncHttpRequestStep
    from app.tasks.steps.http_request_step import AsyncHttpRequestStep

    shared_data = {"requests_list": all_requests, "max_concurrent": max_concurrent}
    async_step = AsyncHttpRequestStep(shared_data)

    try:
        # 执行并发请求
        responses = async_step.run()

        # 解析响应数据
        results = []
        for i, response in enumerate(responses):
            if i >= len(request_metadata):
                continue

            metadata = request_metadata[i]
            processor = metadata["processor"]
            original_data = metadata["original_data"]

            # 解析响应
            post_link = original_data.get("post_link", "")
            stats_data = processor.parse_response(response, post_link)
            if stats_data:
                # 构建绩效数据，使用传入的值
                performance_data = _build_performance_data(
                    stats_data,
                    original_data.get("platform", metadata["platform"]),
                    original_data.get("project_code", ""),
                    original_data.get("kol_id", 0),
                    post_link,
                    original_data.get("social_id", ""),
                )
                results.append(performance_data)
            else:
                logger.warning(f"解析响应失败: {post_link}")

        logger.info(f"批量获取完成，成功获取 {len(results)} 个绩效数据")
        return results

    except Exception as e:
        logger.error(f"批量获取绩效数据失败: {str(e)}")
        return []
