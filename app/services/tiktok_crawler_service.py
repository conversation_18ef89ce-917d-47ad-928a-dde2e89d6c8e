#!/usr/bin/env python
# -*- coding: UTF-8 -*-

"""
@Project : kol-python
<AUTHOR> 小林同学
@Date    : 2025/3/25 下午6:16
@Docs    : 通过 tiktok url 从kol的tk主页获取数据(第三方API)
"""

import datetime
from typing import Any, Dict, List, Optional, Tuple

import aiohttp
import requests
from loguru import logger


class TikTokAPIClient:
    """TikTok API客户端类

    支持同步和异步两种调用方式：

    ```python
    # 同步方式1：使用 with 语句
    with TikTokAPIClient(url) as client:
        user_info, videos = client.scrape_sync()

    # 同步方式2：手动管理资源
    client = TikTokAPIClient(url)
    try:
        user_info, videos = client.scrape_sync()
    finally:
        client.cleanup_sync()

    # 异步方式1：使用 async with 语句
    async with TikTokAPIClient(url) as client:
        user_info, videos = await client.scrape_async()

    # 异步方式2：手动管理资源
    client = TikTokAPIClient(url)
    try:
        await client.init_async()
        user_info, videos = await client.scrape_async()
    finally:
        await client.cleanup_async()
    ```
    """

    # API 配置常量
    API_SEC_USER_ID = "https://api.tikhub.io/api/v1/tiktok/web/get_sec_user_id"
    API_USER_VIDEOS = (
        "https://api.tikhub.io/api/v1/tiktok/app/v3/fetch_user_post_videos"
    )
    # API_TOKEN = "Bearer l7UwT2gt4msspBVL7VWOhnw/hwRb0rODtplUhDz1hEgG+3cxIc4t7xqHFA==" # Leo
    # API_TOKEN = "Bearer 2OQ5cWpxzHErmAg5lGedpGI/0g4psp/Dr5Rf5DbG7WBJ+gwTdfpj10GeIg==" # Zoe
    API_TOKEN = (
        "Bearer xacqaFZO6SIXljVf6mKMul9v/SEVtrzmuotC+SzipUZ5r1dVYtduzyCZUw=="  # KOL
    )

    def __init__(self, url: str, max_videos: int = 20):
        """初始化API客户端实例

        Args:
            url: TikTok 用户主页 URL
            max_videos: 最大获取视频数量，默认为20

        Raises:
            ValueError: URL 格式无效
        """
        self._url = url
        self._max_videos = max(1, min(max_videos, 100))  # 限制在 1-100 之间
        self._sec_user_id: Optional[str] = None
        self._session: Optional[requests.Session] = None
        self._async_session: Optional[aiohttp.ClientSession] = None
        self._initialized: bool = False
        self._async_initialized: bool = False

    def __enter__(self) -> "TikTokAPIClient":
        """上下文管理器入口"""
        self.init_sync()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器退出"""
        self.cleanup_sync()

    async def __aenter__(self) -> "TikTokAPIClient":
        """异步上下文管理器入口"""
        await self.init_async()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """异步上下文管理器退出"""
        await self.cleanup_async()

    @classmethod
    def create(cls, url: str, max_videos: int = 20) -> "TikTokAPIClient":
        """创建API客户端实例的工厂方法

        Args:
            url: TikTok 用户主页 URL
            max_videos: 最大获取视频数量，默认为20

        Returns:
            TikTokAPIClient 实例
        """
        instance = cls(url, max_videos)
        instance.init_sync()
        return instance

    def init_sync(self) -> None:
        """初始化API客户端资源"""
        if not self._initialized:
            self._session = requests.Session()
            self._session.headers.update(
                {
                    "accept": "application/json",
                    "Authorization": self.API_TOKEN,
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                    "AppleWebKit/537.36 (KHTML, like Gecko) "
                    "Chrome/122.0.0.0 Safari/537.36",
                }
            )
            self._initialized = True

    def cleanup_sync(self) -> None:
        """清理API客户端资源"""
        if self._session:
            self._session.close()
            self._session = None
        self._initialized = False

    async def init_async(self) -> None:
        """异步初始化API客户端资源"""
        if not self._async_initialized:
            self._async_session = aiohttp.ClientSession(
                headers={
                    "accept": "application/json",
                    "Authorization": self.API_TOKEN,
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                    "AppleWebKit/537.36 (KHTML, like Gecko) "
                    "Chrome/122.0.0.0 Safari/537.36",
                }
            )
            self._async_initialized = True

    async def cleanup_async(self) -> None:
        """异步清理API客户端资源"""
        if self._async_session:
            await self._async_session.close()
            self._async_session = None
        self._async_initialized = False

    def _get_sec_user_id(self) -> str:
        """获取 TikTok 用户 sec_user_id

        Returns:
            用户 sec_user_id

        Raises:
            ValueError: 获取 sec_user_id 失败
        """
        if not self._sec_user_id:
            if not self._session:
                self.init_sync()

            try:
                response = self._session.get(
                    self.API_SEC_USER_ID,
                    params={"url": self._url},
                    timeout=30,
                )
                response.raise_for_status()

                data = response.json()
                if "data" not in data or not data["data"]:
                    raise ValueError("API返回数据格式错误或用户不存在")

                self._sec_user_id = data["data"]

            except requests.RequestException as e:
                logger.error(f"获取sec_user_id失败: {str(e)}")
                raise ValueError(f"获取sec_user_id失败: {str(e)}")

        if self._sec_user_id is None:
            raise ValueError("sec_user_id 获取失败")
        return self._sec_user_id

    async def _get_sec_user_id_async(self) -> str:
        """异步获取 TikTok 用户 sec_user_id

        Returns:
            用户 sec_user_id

        Raises:
            ValueError: 获取 sec_user_id 失败
        """
        if not self._sec_user_id:
            if not self._async_session:
                await self.init_async()

            try:
                async with self._async_session.get(
                    self.API_SEC_USER_ID,
                    params={"url": self._url},
                    timeout=aiohttp.ClientTimeout(total=30),
                ) as response:
                    response.raise_for_status()
                    data = await response.json()

                    if "data" not in data or not data["data"]:
                        raise ValueError("API返回数据格式错误或用户不存在")

                    self._sec_user_id = data["data"]

            except aiohttp.ClientError as e:
                logger.error(f"异步获取sec_user_id失败: {str(e)}")
                raise ValueError(f"异步获取sec_user_id失败: {str(e)}")

        if self._sec_user_id is None:
            raise ValueError("异步获取 sec_user_id 失败")
        return self._sec_user_id

    def _get_user_videos(self, sec_user_id: str) -> List[Dict[str, Any]]:
        """获取用户视频列表

        Args:
            sec_user_id: 用户 sec_user_id

        Returns:
            视频列表数据

        Raises:
            ValueError: 获取视频列表失败
        """
        if not self._session:
            self.init_sync()

        try:
            response = self._session.get(
                self.API_USER_VIDEOS,
                params={
                    "sec_user_id": sec_user_id,
                    "max_cursor": 0,
                    "count": self._max_videos,
                    "sort_type": 0,
                },
                timeout=30,
            )
            response.raise_for_status()

            data = response.json()
            if (
                "data" not in data
                or "aweme_list" not in data["data"]
                or not data["data"]["aweme_list"]
            ):
                raise ValueError("API返回数据格式错误或没有视频")

            return data["data"]["aweme_list"]

        except requests.RequestException as e:
            logger.error(f"获取用户视频失败: {str(e)}")
            raise ValueError(f"获取用户视频失败: {str(e)}")

    async def _get_user_videos_async(self, sec_user_id: str) -> List[Dict[str, Any]]:
        """异步获取用户视频列表

        Args:
            sec_user_id: 用户 sec_user_id

        Returns:
            视频列表数据

        Raises:
            ValueError: 获取视频列表失败
        """
        if not self._async_session:
            await self.init_async()

        try:
            async with self._async_session.get(
                self.API_USER_VIDEOS,
                params={
                    "sec_user_id": sec_user_id,
                    "max_cursor": 0,
                    "count": self._max_videos,
                    "sort_type": 0,
                },
                timeout=aiohttp.ClientTimeout(total=30),
            ) as response:
                response.raise_for_status()
                data = await response.json()

                if (
                    "data" not in data
                    or "aweme_list" not in data["data"]
                    or not data["data"]["aweme_list"]
                ):
                    raise ValueError("API返回数据格式错误或没有视频")

                return data["data"]["aweme_list"]

        except aiohttp.ClientError as e:
            logger.error(f"异步获取用户视频失败: {str(e)}")
            raise ValueError(f"异步获取用户视频失败: {str(e)}")

    def _extract_user_info(self, video_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """从视频数据中提取用户信息

        Args:
            video_data: 视频数据列表

        Returns:
            用户信息字典
        """
        if not video_data:
            return {
                "username": "",
                "kol_id": "",
                "followers_count": 0,
                "likes_count": 0,
                "bio": "",
            }

        author = video_data[0].get("author", {})

        return {
            "username": author.get("nickname", ""),
            "kol_id": author.get("unique_id", "") or author.get("ins_id", ""),
            "followers_count": author.get("follower_count", 0),
            "likes_count": author.get("total_favorited", 0),
            "bio": author.get("signature", ""),
        }

    def _extract_video_info(
        self, video_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """从视频数据中提取视频信息

        Args:
            video_data: 视频数据列表

        Returns:
            视频信息字典列表
        """
        videos = []

        for item in video_data:
            # 提取基本信息以兼容原接口
            video_id = item.get("aweme_id", "")
            statistics = item.get("statistics", {})
            play_count = statistics.get("play_count", 0)

            # 检查是否为置顶视频
            is_pinned = item.get("is_top", 0) == 1

            # 创建视频信息字典
            video_info = {
                "video_id": video_id,
                "play_count": play_count,
                "is_pinned": is_pinned,
            }

            # 视频URL和分享信息
            video_info["share_url"] = item.get("share_info", {}).get("share_url", "")
            video_info["desc"] = item.get("desc", "")
            video_info["desc_language"] = item.get("desc_language", "")

            # 视频播放地址
            video_url = ""
            bit_rate = item.get("video", {}).get("bit_rate", [])
            if bit_rate and len(bit_rate) > 0:
                play_addr = bit_rate[0].get("play_addr", {})
                url_list = play_addr.get("url_list", [])
                if url_list and len(url_list) > 0:
                    video_url = url_list[0]
            video_info["video_url"] = video_url

            # 音乐信息
            music_url = (
                item.get("added_sound_music_info", {})
                .get("play_url", {})
                .get("uri", "")
            )
            video_info["music_url"] = music_url

            # 详细的互动数据
            video_info["likes_count"] = statistics.get("digg_count", 0)
            video_info["comments_count"] = statistics.get("comment_count", 0)
            video_info["shares_count"] = statistics.get("share_count", 0)
            video_info["collect_count"] = statistics.get("collect_count", 0)

            # 创建时间(将时间戳转换为数据库DateTime格式)
            create_time = item.get("create_time", 0)
            if create_time:
                video_info["create_time"] = datetime.datetime.fromtimestamp(create_time)

            # hashtag 话题标签
            hashtags = []
            if item.get("original_client_text", {}).get("text_extra", []):
                for tag in item["original_client_text"]["text_extra"]:
                    hashtags.append(tag.get("hashtag_name", ""))
                if hashtags:
                    video_info["hashtags"] = hashtags

            videos.append(video_info)

        return videos

    def scrape_sync(
        self, sec_user_id: Optional[str] = None
    ) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """同步方式获取用户信息和视频列表

        Returns:
            包含用户信息和视频列表的元组

        Raises:
            ValueError: 获取数据失败
        """
        if not self._initialized:
            self.init_sync()

        try:
            # 获取用户ID
            if not sec_user_id:
                sec_user_id = self._get_sec_user_id()

            # 获取视频列表
            video_data = self._get_user_videos(sec_user_id)

            # 提取用户信息和视频信息
            user_info = self._extract_user_info(video_data)
            videos = self._extract_video_info(video_data)

            return user_info, videos

        except Exception as e:
            logger.error(f"获取数据失败: {str(e)}")
            raise ValueError(f"获取数据失败: {str(e)}")

    async def scrape_async(
        self, sec_user_id: Optional[str] = None
    ) -> Tuple[Dict[str, Any], List[Dict[str, Any]], bool]:
        """异步方式获取用户信息和视频列表

        Args:
            sec_user_id: 可选的用户ID，如果不提供则自动获取

        Returns:
            包含用户信息和视频列表的元组

        Raises:
            ValueError: 获取数据失败
        """
        if not self._async_initialized:
            await self.init_async()

        try:
            # 获取用户ID
            if not sec_user_id:
                sec_user_id = await self._get_sec_user_id_async()

            # 获取视频列表
            video_data = await self._get_user_videos_async(sec_user_id)

            # 提取用户信息和视频信息
            user_info = self._extract_user_info(video_data)
            videos = self._extract_video_info(video_data)

            return user_info, videos, True

        except Exception as e:
            logger.error(f"异步获取数据失败: {str(e)}")
            return {}, [], False
            # raise ValueError(f"异步获取数据失败: {str(e)}")

    def scrape(self) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """获取用户信息和视频列表（与scrape_sync相同，保持接口一致性）

        Returns:
            包含用户信息和视频列表的元组
        """
        return self.scrape_sync()


# 示例用法
if __name__ == "__main__":
    # 同步用法
    with TikTokAPIClient("https://www.tiktok.com/@icedcaramelriss") as client:
        user_info, videos = client.scrape_sync()
        print(f"同步用户名: {user_info}")

    # 异步用法
    async def async_example():
        async with TikTokAPIClient("https://www.tiktok.com/@icedcaramelriss") as client:
            user_info, videos, tag = await client.scrape_async()
            print(f"异步用户名: {user_info}")

    # 运行异步示例
    # asyncio.run(async_example())
