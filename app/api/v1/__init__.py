from fastapi import APIRouter

# API endpoints - 基于新的数据模型
from app.api.v1.endpoints import (
    candidates,
    concurrency_management,
    crawler_tasks,
    email_send_logs,
    email_services,
    email_templates,
    kol_match,
    kol_performance_view,
    kols,
    nano_tools,
    payments,
    performance_payments,
    performances,
    projects,
    websocket_tasks,
)

api_router = APIRouter()

# API routes
api_router.include_router(projects.router, prefix="/projects", tags=["项目管理"])
api_router.include_router(
    email_templates.router, prefix="/email-templates", tags=["邮件模板管理"]
)
api_router.include_router(
    crawler_tasks.router, prefix="/crawler-tasks", tags=["爬虫任务管理"]
)
api_router.include_router(kols.router, prefix="/kols", tags=["KOL管理"])
api_router.include_router(
    email_send_logs.router, prefix="/email-send-logs", tags=["邮件日志管理"]
)
api_router.include_router(candidates.router, prefix="/candidates", tags=["候选人管理"])
api_router.include_router(
    performances.router, prefix="/performances", tags=["绩效管理"]
)
api_router.include_router(payments.router, prefix="/payments", tags=["支付管理"])
api_router.include_router(
    performance_payments.router,
    prefix="/performance-payments",
    tags=["绩效支付统一管理"],
)
api_router.include_router(
    kol_performance_view.router, prefix="/kol-performance", tags=["KOL表现视图"]
)
api_router.include_router(kol_match.router, prefix="/kol-match", tags=["KOL匹配评估"])
api_router.include_router(nano_tools.router, prefix="/nano-tools", tags=["Nano工具"])
api_router.include_router(
    email_services.router, prefix="/email-services", tags=["邮件服务"]
)
api_router.include_router(websocket_tasks.router, prefix="", tags=["WebSocket任务通信"])
api_router.include_router(
    concurrency_management.router, prefix="/concurrency", tags=["并发控制管理"]
)

# Legacy API routes (注释掉不存在的文件)
# api_router.include_router(filter_data.router, prefix="/filter-data", tags=["Legacy - filter_data 管理"])
# api_router.include_router(kol_info.router, prefix="/kol", tags=["Legacy - kol_info 管理"])
# api_router.include_router(video_info.router, prefix="/videos", tags=["Legacy - video_info 管理"])
# api_router.include_router(send_data.router, prefix="/send-data", tags=["Legacy - send_data 管理"])
# api_router.include_router(filter_kol_association.router, prefix="/filter-kol-associations", tags=["Legacy - filter_name 和 kol_info 关联"])
# api_router.include_router(tasks.router, prefix="/tasks", tags=["Legacy - tasks 管理"])
# api_router.include_router(candidate_data.router, prefix="/candidate-data", tags=["Legacy - candidate_data 管理"])
# api_router.include_router(collaboration_performance.router, prefix="/collaboration-performance", tags=["Legacy - collaboration_performance 管理"])
# api_router.include_router(tag.router, prefix="/tags", tags=["Legacy - tag 管理"])
# api_router.include_router(kol_tag_association.router, prefix="/kol-tag-associations", tags=["Legacy - kol_info 和 tag 关联"])
# api_router.include_router(project_tag_association.router, prefix="/project-tag-associations", tags=["Legacy - project 和 tag 关联"])
