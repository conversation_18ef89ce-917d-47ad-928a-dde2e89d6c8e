"""
候选人管理API端点
"""

from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import candidate
from app.models.enums import FollowUpStatusEnum, PlatformEnum
from app.schemas.candidate import (
    Candidate,
    CandidateCreate,
    CandidateListResponse,
    CandidateSearch,
    CandidateStatsResponse,
    CandidateStatusUpdate,
    CandidateTrackerAssign,
    CandidateUpdate,
)

router = APIRouter()


@router.get("/", response_model=CandidateListResponse)
def get_candidates(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    platform: Optional[PlatformEnum] = Query(None, description="平台"),
    status: Optional[FollowUpStatusEnum] = Query(None, description="跟进状态"),
    tracker: Optional[str] = Query(None, description="跟进人"),
    need_review: Optional[bool] = Query(None, description="是否需要人工审核"),
    db: Session = Depends(get_db),
):
    """获取候选人列表"""
    if project_code:
        candidates = candidate.get_by_project(
            db, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            candidate.get_by_project(db, project_code=project_code, skip=0, limit=10000)
        )
    elif platform:
        candidates = candidate.get_by_platform(
            db, platform=platform, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            candidate.get_by_platform(
                db, platform=platform, project_code=project_code, skip=0, limit=10000
            )
        )
    elif status:
        candidates = candidate.get_by_status(
            db, status=status, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            candidate.get_by_status(
                db, status=status, project_code=project_code, skip=0, limit=10000
            )
        )
    elif tracker:
        candidates = candidate.get_by_tracker(
            db, tracker=tracker, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            candidate.get_by_tracker(
                db, tracker=tracker, project_code=project_code, skip=0, limit=10000
            )
        )
    elif need_review is not None:
        candidates = candidate.get_by_need_review(
            db,
            need_review=need_review,
            project_code=project_code,
            skip=skip,
            limit=limit,
        )
        total = len(
            candidate.get_by_need_review(
                db,
                need_review=need_review,
                project_code=project_code,
                skip=0,
                limit=10000,
            )
        )
    else:
        candidates = candidate.get_multi(db, skip=skip, limit=limit)
        total = len(candidate.get_multi(db, skip=0, limit=10000))

    return CandidateListResponse(items=candidates, total=total, skip=skip, limit=limit)


@router.post("/search", response_model=CandidateListResponse)
def search_candidates(
    search_params: CandidateSearch,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
):
    """搜索候选人"""
    candidates = candidate.search_candidates(
        db,
        nick_name=search_params.nick_name,
        social_id=search_params.social_id,
        platform=search_params.platform,
        status=search_params.status,
        tracker=search_params.tracker,
        project_code=search_params.project_code,
        reply_email=search_params.reply_email,
        need_review=search_params.need_review,
        skip=skip,
        limit=limit,
    )

    # 计算总数
    total = len(
        candidate.search_candidates(
            db,
            nick_name=search_params.nick_name,
            social_id=search_params.social_id,
            platform=search_params.platform,
            status=search_params.status,
            tracker=search_params.tracker,
            project_code=search_params.project_code,
            reply_email=search_params.reply_email,
            need_review=search_params.need_review,
            skip=0,
            limit=10000,
        )
    )

    return CandidateListResponse(items=candidates, total=total, skip=skip, limit=limit)


@router.get("/need-review", response_model=CandidateListResponse)
def get_candidates_need_review(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    need_review: bool = Query(True, description="是否需要人工审核"),
    db: Session = Depends(get_db),
):
    """获取需要人工审核的候选人列表"""
    candidates = candidate.get_by_need_review(
        db, need_review=need_review, project_code=project_code, skip=skip, limit=limit
    )
    total = len(
        candidate.get_by_need_review(
            db, need_review=need_review, project_code=project_code, skip=0, limit=10000
        )
    )

    return CandidateListResponse(items=candidates, total=total, skip=skip, limit=limit)


@router.get("/stats", response_model=CandidateStatsResponse)
def get_candidate_stats(
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db),
):
    """获取候选人统计信息"""
    stats = candidate.get_candidate_statistics(db, project_code=project_code)
    return CandidateStatsResponse(**stats)


@router.get("/by-kol/{kol_id}", response_model=Candidate)
def get_candidate_by_kol(kol_id: int, db: Session = Depends(get_db)):
    """根据KOL ID获取候选人"""
    db_candidate = candidate.get_by_kol_id(db, kol_id=kol_id)
    if not db_candidate:
        raise HTTPException(status_code=404, detail="候选人不存在")
    return db_candidate


@router.get("/without-kol", response_model=CandidateListResponse)
def get_candidates_without_kol(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db),
):
    """获取没有关联KOL的候选人列表"""
    candidates = candidate.get_candidates_without_kol(
        db, project_code=project_code, skip=skip, limit=limit
    )
    total = len(
        candidate.get_candidates_without_kol(
            db, project_code=project_code, skip=0, limit=10000
        )
    )

    return CandidateListResponse(items=candidates, total=total, skip=skip, limit=limit)


@router.get("/by-thread/{thread_id}", response_model=Candidate)
def get_candidate_by_thread(thread_id: str, db: Session = Depends(get_db)):
    """根据邮件线程ID获取候选人"""
    db_candidate = candidate.get_by_thread_id(db, thread_id=thread_id)
    if not db_candidate:
        raise HTTPException(status_code=404, detail="候选人不存在")
    return db_candidate


@router.get("/{candidate_id}", response_model=Candidate)
def get_candidate(candidate_id: int, db: Session = Depends(get_db)):
    """根据候选人ID获取候选人详情"""
    db_candidate = candidate.get(db, id=candidate_id)
    if not db_candidate:
        raise HTTPException(status_code=404, detail="候选人不存在")
    return db_candidate


@router.post("/", response_model=Candidate)
def create_candidate(candidate_in: CandidateCreate, db: Session = Depends(get_db)):
    """创建新候选人"""
    try:
        return candidate.create_candidate(db, obj_in=candidate_in)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{candidate_id}", response_model=Candidate)
def update_candidate(
    candidate_id: int, candidate_in: CandidateUpdate, db: Session = Depends(get_db)
):
    """更新候选人信息"""
    db_candidate = candidate.get(db, id=candidate_id)
    if not db_candidate:
        raise HTTPException(status_code=404, detail="候选人不存在")
    try:
        return candidate.update_candidate(db, db_obj=db_candidate, obj_in=candidate_in)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/{candidate_id}/status", response_model=Candidate)
def update_candidate_status(
    candidate_id: int,
    status_update: CandidateStatusUpdate,
    db: Session = Depends(get_db),
):
    """更新候选人跟进状态"""
    db_candidate = candidate.update_follow_up_status(
        db,
        candidate_id=candidate_id,
        status=status_update.status,
        note=status_update.note,
    )
    if not db_candidate:
        raise HTTPException(status_code=404, detail="候选人不存在")
    return db_candidate


@router.patch("/{candidate_id}/tracker", response_model=Candidate)
def assign_tracker(
    candidate_id: int,
    tracker_assign: CandidateTrackerAssign,
    db: Session = Depends(get_db),
):
    """分配跟进人"""
    db_candidate = candidate.assign_tracker(
        db, candidate_id=candidate_id, tracker=tracker_assign.tracker
    )
    if not db_candidate:
        raise HTTPException(status_code=404, detail="候选人不存在")
    return db_candidate


@router.patch("/{candidate_id}/link-kol/{kol_id}", response_model=Candidate)
def link_candidate_to_kol(
    candidate_id: int,
    kol_id: int,
    update_platform: bool = Query(True, description="是否同步更新平台信息"),
    db: Session = Depends(get_db),
):
    """将候选人关联到KOL"""
    try:
        db_candidate = candidate.link_candidate_to_kol(
            db,
            candidate_id=candidate_id,
            kol_id=kol_id,
            update_platform=update_platform,
        )
        if not db_candidate:
            raise HTTPException(status_code=404, detail="候选人不存在")
        return db_candidate
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{candidate_id}", response_model=Candidate)
def delete_candidate(candidate_id: int, db: Session = Depends(get_db)):
    """删除候选人"""
    db_candidate = candidate.get(db, id=candidate_id)
    if not db_candidate:
        raise HTTPException(status_code=404, detail="候选人不存在")
    return candidate.remove(db, id=candidate_id)
