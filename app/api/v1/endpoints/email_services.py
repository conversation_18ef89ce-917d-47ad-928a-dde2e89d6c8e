"""
邮件服务API端点
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import email_send_logs, email_template, kol
from app.logging_config import get_logger
from app.models.enums import EmailSendStatusEnum
from app.schemas.email_send_logs import EmailSendCreateInternal
from app.services.email_services import PostmarkEmailSender

router = APIRouter()
logger = get_logger("email_services")


def _record_email_send(
    db: Session,
    kol_id: int,
    template_code: str,
    project_code: Optional[str],
    from_email: str,
    to_email: str,
    platform: Optional[str],
    send_result: Dict[str, Any],
    note: Optional[str] = None,
) -> int:
    """记录邮件发送信息到email_send表"""
    try:
        # 确定发送状态 - Postmark API使用ErrorCode=0表示成功
        is_success = send_result.get("ErrorCode") == 0 or send_result.get(
            "success", False
        )
        send_status = (
            EmailSendStatusEnum.SENT if is_success else EmailSendStatusEnum.FAILED
        )

        # 构建备注信息
        result_note = note or ""
        if send_result.get("error"):
            result_note += f" 错误: {send_result['error']}"
        if send_result.get("Message"):
            result_note += f" 消息: {send_result['Message']}"
        if send_result.get("MessageID"):
            result_note += f" MessageID: {send_result['MessageID']}"

        # 转换平台字符串为枚举
        platform_enum = None
        if platform:
            try:
                from app.models.enums import PlatformEnum

                platform_enum = PlatformEnum(platform.upper())
            except ValueError:
                logger.warning(f"无效的平台类型: {platform}")

        # 创建邮件发送记录 - 需要先查找 KOL 的 social_id
        from app.crud import kol as kol_crud

        kol_obj = kol_crud.get_by_kol_id(db, kol_id=kol_id)
        if not kol_obj:
            logger.error(f"无法找到 KOL ID {kol_id}")
            return 0

        # 使用内部创建方法
        email_send_data = EmailSendCreateInternal(
            platform=platform_enum,
            kol_id=kol_id,
            project_code=project_code or "",
            template_code=template_code,
            from_email=from_email,
            to_email=to_email,
            note=result_note.strip(),
        )

        # 保存到数据库
        email_send_record = email_send_logs.create_internal(db, obj_in=email_send_data)

        # 更新发送状态和时间
        email_send_logs.update(
            db,
            db_obj=email_send_record,
            obj_in={
                "send_status": send_status,
                "send_date": datetime.now(timezone.utc),
            },
        )

        logger.info(
            f"邮件发送记录已保存: ID={email_send_record.id}, 状态={send_status.value}, 收件人={to_email}"
        )
        return email_send_record.id

    except Exception as e:
        logger.error(f"保存邮件发送记录失败: {str(e)}")
        return 0


# 移除不需要的 EmailRecipient 和 BatchEmailRequest schema


class SingleEmailRequest(BaseModel):
    """单个邮件发送请求"""

    template_code: str = Field(..., description="邮件模板代码", max_length=50)
    social_id: str = Field(..., description="KOL社交媒体ID")
    platform: str = Field(..., description="平台")
    project_code: str = Field(..., description="项目编码", max_length=50)


class KOLIdentifier(BaseModel):
    """KOL标识符"""

    social_id: str = Field(..., description="KOL社交媒体ID")
    platform: str = Field(..., description="平台")
    project_code: str = Field(..., description="项目编码")


class BatchEmailByKolsRequest(BaseModel):
    """批量发送邮件给KOL列表请求"""

    template_code: str = Field(..., description="邮件模板代码", max_length=50)
    kols: List[KOLIdentifier] = Field(
        ..., description="KOL标识符列表", min_length=1, max_length=100
    )


class EmailSendResponse(BaseModel):
    """邮件发送响应"""

    success: bool = Field(..., description="发送是否成功")
    message: str = Field(..., description="响应消息")
    sent_count: int = Field(0, description="成功发送数量")
    failed_count: int = Field(0, description="失败数量")
    details: Optional[List[Dict[str, Any]]] = Field(None, description="详细结果")


# 移除不需要的 PendingEmailsResponse schema


# 移除 send_batch_emails 端点
# 移除 send_batch_emails 函数


@router.post("/send-single", response_model=EmailSendResponse)
def send_single_email(request: SingleEmailRequest, db: Session = Depends(get_db)):
    """发送单个邮件给指定KOL"""
    try:
        # 根据 social_id, platform, project_code 查找 KOL
        from app.models.enums import PlatformEnum

        try:
            platform_enum = PlatformEnum(request.platform.upper())
        except ValueError:
            raise HTTPException(
                status_code=400, detail=f"不支持的平台: {request.platform}"
            )

        kol_obj = kol.get_by_social_id_platform_project(
            db,
            social_id=request.social_id,
            platform=platform_enum,
            project_code=request.project_code,
        )
        if not kol_obj:
            raise HTTPException(status_code=404, detail="未找到对应的KOL")

        if not kol_obj.email:
            raise HTTPException(status_code=400, detail="KOL没有邮箱地址")

        # 获取邮件模板
        if not request.project_code:
            raise HTTPException(status_code=400, detail="项目编码不能为空")
        template = email_template.get_by_code_and_project(
            db, code=request.template_code, project_code=request.project_code
        )
        if not template:
            raise HTTPException(status_code=404, detail="邮件模板不存在")

        if not template.postmark_token:
            raise HTTPException(
                status_code=400, detail="邮件模板缺少Postmark Token配置"
            )

        if not template.from_email:
            raise HTTPException(status_code=400, detail="邮件模板缺少发件人邮箱配置")

        # 初始化Postmark发送器
        sender = PostmarkEmailSender(template.postmark_token)

        # 构建账户链接
        account_link = (
            f"https://{kol_obj.platform.value.lower()}.com/@{kol_obj.social_id}"
        )

        # 准备收件人数据
        recipients_data = [
            {
                "email": kol_obj.email,
                "name": kol_obj.nick_name,
                "account_link": account_link,
            }
        ]

        # 发送邮件
        results = sender.batch_send_template_emails(
            from_email=template.from_email,
            template_code=template.code,  # 将模板ID转换为字符串作为template_code
            recipients=recipients_data,
        )

        # 记录邮件发送信息到数据库
        email_send_id = 0
        if results:
            try:
                email_send_id = _record_email_send(
                    db=db,
                    kol_id=kol_obj.id,
                    template_code=request.template_code,
                    project_code=request.project_code,
                    from_email=template.from_email,
                    to_email=kol_obj.email,
                    platform=kol_obj.platform.value,
                    send_result=results[0],
                    note=f"单个邮件发送 - social_id: {request.social_id}, KOL: {kol_obj.nick_name}",
                )
            except Exception as e:
                logger.error(f"记录邮件发送信息失败: {str(e)}")

        # 处理结果 - Postmark API使用ErrorCode=0表示成功
        if results and (
            results[0].get("ErrorCode") == 0 or results[0].get("success", False)
        ):
            return EmailSendResponse(
                success=True,
                message=f"邮件发送成功：{kol_obj.email}，发送记录ID: {email_send_id}",
                sent_count=1,
                failed_count=0,
                details=results,
            )
        else:
            error_msg = (
                results[0].get("Message", results[0].get("error", "未知错误"))
                if results
                else "发送失败"
            )
            return EmailSendResponse(
                success=False,
                message=f"邮件发送失败：{error_msg}，发送记录ID: {email_send_id}",
                sent_count=0,
                failed_count=1,
                details=results,
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送单个邮件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发送失败: {str(e)}")


@router.post("/send-batch-kols", response_model=EmailSendResponse)
def send_batch_emails_to_kols(
    request: BatchEmailByKolsRequest, db: Session = Depends(get_db)
):
    """批量发送邮件给KOL列表"""
    try:
        # 验证输入参数
        if not request.kols:
            raise HTTPException(status_code=400, detail="KOL标识符列表不能为空")

        # 获取第一个KOL的项目编码用于查找模板（假设所有KOL使用相同的模板）
        first_kol = request.kols[0]
        template = email_template.get_by_code_and_project(
            db, code=request.template_code, project_code=first_kol.project_code
        )
        if not template:
            raise HTTPException(status_code=404, detail="邮件模板不存在")

        if not template.postmark_token:
            raise HTTPException(
                status_code=400, detail="邮件模板缺少Postmark Token配置"
            )

        if not template.from_email:
            raise HTTPException(status_code=400, detail="邮件模板缺少发件人邮箱配置")

        # 根据每个 KOL 标识符查找对应的 KOL
        from app.models.enums import PlatformEnum

        kol_objects = []

        for kol_identifier in request.kols:
            try:
                platform_enum = PlatformEnum(kol_identifier.platform.upper())
            except ValueError:
                logger.warning(
                    f"不支持的平台: {kol_identifier.platform}，跳过 KOL {kol_identifier.social_id}"
                )
                continue

            kol_obj = kol.get_by_social_id_platform_project(
                db,
                social_id=kol_identifier.social_id,
                platform=platform_enum,
                project_code=kol_identifier.project_code,
            )
            if kol_obj and kol_obj.email:
                kol_objects.append(kol_obj)
            else:
                logger.warning(
                    f"KOL {kol_identifier.social_id} (平台: {kol_identifier.platform}, 项目: {kol_identifier.project_code}) 不存在或没有邮箱地址"
                )

        if not kol_objects:
            raise HTTPException(status_code=400, detail="没有找到有效的KOL邮箱地址")

        # 初始化Postmark发送器
        sender = PostmarkEmailSender(template.postmark_token)

        # 准备收件人数据
        recipients_data = []
        for kol_obj in kol_objects:
            account_link = (
                f"https://{kol_obj.platform.value.lower()}.com/@{kol_obj.social_id}"
            )
            recipients_data.append(
                {
                    "email": kol_obj.email,
                    "name": kol_obj.social_id,
                    "account_link": account_link,
                }
            )

        # 发送邮件
        results = sender.batch_send_template_emails(
            from_email=template.from_email,
            template_code=template.code,  # 将模板ID转换为字符串作为template_code
            recipients=recipients_data,
        )

        # 记录邮件发送信息到数据库
        email_send_ids = []
        for i, result in enumerate(results):
            if i < len(kol_objects):
                kol_obj = kol_objects[i]
                try:
                    email_send_id = _record_email_send(
                        db=db,
                        kol_id=kol_obj.id,
                        template_code=request.template_code,
                        project_code=kol_obj.project_code,  # 使用 KOL 对象中的项目编码
                        from_email=template.from_email,
                        to_email=kol_obj.email,
                        platform=kol_obj.platform.value,
                        send_result=result,
                        note=f"批量发送给KOL - social_id: {kol_obj.social_id}, KOL: {kol_obj.nick_name}",
                    )
                    email_send_ids.append(email_send_id)
                except Exception as e:
                    logger.error(f"记录邮件发送信息失败: {str(e)}")

        # 统计结果 - Postmark API使用ErrorCode=0表示成功
        sent_count = sum(
            1 for r in results if (r.get("ErrorCode") == 0 or r.get("success", False))
        )
        failed_count = len(results) - sent_count

        return EmailSendResponse(
            success=failed_count == 0,
            message=f"批量发送给KOL完成：成功 {sent_count} 条，失败 {failed_count} 条，已记录 {len(email_send_ids)} 条发送记录",
            sent_count=sent_count,
            failed_count=failed_count,
            details=results,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量发送邮件给KOL失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发送失败: {str(e)}")


# 移除其他不需要的端点
# 所有其他端点已移除
