"""
绩效管理API端点
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import performance
from app.models.enums import PlatformEnum
from app.schemas.performance import (
    Performance,
    PerformanceCreate,
    PerformanceListResponse,
    PerformanceSearch,
    PerformanceStatsResponse,
    PerformanceUpdate,
)

router = APIRouter()


@router.get("/", response_model=PerformanceListResponse)
def get_performances(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    kol_id: Optional[int] = Query(None, description="KOL ID"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    platform: Optional[PlatformEnum] = Query(None, description="平台"),
    db: Session = Depends(get_db),
):
    """获取绩效记录列表"""
    if kol_id:
        performances = performance.get_by_kol(db, kol_id=kol_id, skip=skip, limit=limit)
        total = len(performance.get_by_kol(db, kol_id=kol_id, skip=0, limit=10000))
    elif project_code:
        performances = performance.get_by_project(
            db, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            performance.get_by_project(
                db, project_code=project_code, skip=0, limit=10000
            )
        )
    elif platform:
        performances = performance.get_by_platform(
            db, platform=platform, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            performance.get_by_platform(
                db, platform=platform, project_code=project_code, skip=0, limit=10000
            )
        )

    else:
        performances = performance.get_multi(db, skip=skip, limit=limit)
        total = len(performance.get_multi(db, skip=0, limit=10000))

    return PerformanceListResponse(
        items=performances, total=total, skip=skip, limit=limit
    )


@router.post("/search", response_model=PerformanceListResponse)
def search_performances(
    search_params: PerformanceSearch,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
):
    """搜索绩效记录"""
    performances = performance.search_performances(
        db,
        kol_id=search_params.kol_id,
        platform=search_params.platform,
        project_code=search_params.project_code,
        min_views=search_params.min_views,
        max_views=search_params.max_views,
        skip=skip,
        limit=limit,
    )

    # 计算总数
    total = len(
        performance.search_performances(
            db,
            kol_id=search_params.kol_id,
            platform=search_params.platform,
            project_code=search_params.project_code,
            min_views=search_params.min_views,
            max_views=search_params.max_views,
            skip=0,
            limit=10000,
        )
    )

    return PerformanceListResponse(
        items=performances, total=total, skip=skip, limit=limit
    )


@router.get("/stats", response_model=PerformanceStatsResponse)
def get_performance_stats(
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db),
):
    """获取绩效统计信息"""
    stats = performance.get_performance_statistics(db, project_code=project_code)
    return PerformanceStatsResponse(**stats)


@router.get("/top-performers", response_model=List[Performance])
def get_top_performers(
    project_code: Optional[str] = Query(None, description="项目编码"),
    platform: Optional[PlatformEnum] = Query(None, description="平台"),
    limit: int = Query(10, ge=1, le=100, description="返回的记录数"),
    db: Session = Depends(get_db),
):
    """获取表现最佳的绩效记录"""
    return performance.get_top_performers(
        db, project_code=project_code, platform=platform, limit=limit
    )


@router.get("/{performance_id}", response_model=Performance)
def get_performance(performance_id: int, db: Session = Depends(get_db)):
    """根据绩效ID获取绩效详情"""
    db_performance = performance.get(db, id=performance_id)
    if not db_performance:
        raise HTTPException(status_code=404, detail="绩效记录不存在")
    return db_performance


@router.get("/by-post-link/{post_link:path}", response_model=Performance)
def get_performance_by_post_link(post_link: str, db: Session = Depends(get_db)):
    """根据帖子链接获取绩效记录"""
    db_performance = performance.get_by_post_link(db, post_link=post_link)
    if not db_performance:
        raise HTTPException(status_code=404, detail="绩效记录不存在")
    return db_performance


@router.post("/", response_model=Performance)
def create_performance(
    performance_in: PerformanceCreate, db: Session = Depends(get_db)
):
    """创建新绩效记录"""
    try:
        return performance.create_performance(db, obj_in=performance_in)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{performance_id}", response_model=Performance)
def update_performance(
    performance_id: int,
    performance_in: PerformanceUpdate,
    db: Session = Depends(get_db),
):
    """更新绩效记录"""
    db_performance = performance.get(db, id=performance_id)
    if not db_performance:
        raise HTTPException(status_code=404, detail="绩效记录不存在")
    return performance.update(db, db_obj=db_performance, obj_in=performance_in)


@router.delete("/{performance_id}", response_model=Performance)
def delete_performance(performance_id: int, db: Session = Depends(get_db)):
    """删除绩效记录"""
    db_performance = performance.get(db, id=performance_id)
    if not db_performance:
        raise HTTPException(status_code=404, detail="绩效记录不存在")
    return performance.remove(db, id=performance_id)
