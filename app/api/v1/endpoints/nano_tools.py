"""
Nano工具API端点
"""

import asyncio
import uuid
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import kol
from app.logging_config import get_logger
from app.services.nano_service import NanoService
from app.utils.task_cleanup import (
    cleanup_old_tasks,
    force_cleanup_task,
    get_task_store_stats,
)

router = APIRouter()
logger = get_logger("nano_tools")

# 内存中存储任务状态（生产环境建议使用Redis）
task_status_store = {}


class NanoEmailRequest(BaseModel):
    """Nano邮箱获取请求schema"""

    kol_id: int = Field(..., description="KOL ID")
    force_update: bool = Field(False, description="是否强制更新，跳过已调用检查")


class NanoEmailResponse(BaseModel):
    """Nano邮箱获取响应schema"""

    kol_id: int = Field(..., description="KOL ID")
    email: Optional[str] = Field(None, description="获取到的邮箱")
    topics: Optional[List[str]] = Field(
        default_factory=list, description="获取到的topics"
    )
    success: bool = Field(..., description="获取是否成功")
    message: Optional[str] = Field(None, description="结果消息")


class BatchNanoEmailRequest(BaseModel):
    """批量Nano邮箱获取请求schema"""

    kol_ids: List[int] = Field(..., description="KOL ID列表")
    force_update: bool = Field(False, description="是否强制更新，跳过已调用检查")


class BatchNanoEmailResponse(BaseModel):
    """批量Nano邮箱获取响应schema"""

    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    results: List[NanoEmailResponse] = Field(..., description="详细结果列表")


class TaskStartResponse(BaseModel):
    """任务启动响应schema"""

    task_id: str = Field(..., description="任务ID")
    message: str = Field(..., description="启动消息")


class TaskStatusResponse(BaseModel):
    """任务状态响应schema"""

    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态: pending/running/completed/failed")
    progress: int = Field(..., description="进度百分比 0-100")
    total_count: int = Field(..., description="总数量")
    processed_count: int = Field(..., description="已处理数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    message: Optional[str] = Field(None, description="状态消息")
    results: Optional[List[NanoEmailResponse]] = Field(
        None, description="完成时的结果列表"
    )
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


@router.post("/fetch-email", response_model=NanoEmailResponse)
def fetch_email_from_nano(request: NanoEmailRequest, db: Session = Depends(get_db)):
    """使用Nano接口获取KOL邮箱"""
    try:
        # 检查KOL是否存在
        db_kol = kol.get_by_kol_id(db, kol_id=request.kol_id)
        if not db_kol:
            raise HTTPException(status_code=404, detail="KOL不存在")

        # 检查是否已经调用过nano接口（除非强制更新）
        if db_kol.nano_email_fetched_at and not request.force_update:
            return NanoEmailResponse(
                kol_id=request.kol_id,
                email=db_kol.nano_extracted_email,
                topics=db_kol.topics or [],
                success=True,
                message="已调用过nano接口",
            )

        # 检查KOL是否通过AI匹配(除非强制更新)
        if not db_kol.ai_matched and not request.force_update:
            return NanoEmailResponse(
                kol_id=request.kol_id,
                email=None,
                topics=[],
                success=False,
                message="KOL未通过AI匹配，不允许调用nano接口",
            )

        # 构建KOL的URL
        kol_url = _build_kol_url(db_kol)
        if not kol_url:
            return NanoEmailResponse(
                kol_id=request.kol_id,
                email=None,
                topics=[],
                success=False,
                message="无法构建KOL的URL",
            )

        # 调用nano服务
        nano_service = NanoService()
        result = nano_service.get_kol_email_by_url(kol_url)

        # 处理nano服务返回结果
        extracted_email = None
        topics = []
        if result and isinstance(result, dict):
            extracted_email = result.get("email")
            topics = result.get("topics", [])
        elif isinstance(result, str):
            extracted_email = result

        # 更新数据库状态
        kol.update_nano_email_status(
            db, kol_id=request.kol_id, extracted_email=extracted_email, topics=topics
        )

        return NanoEmailResponse(
            kol_id=request.kol_id,
            email=extracted_email,
            topics=topics,
            success=True,
            message="nano接口调用成功",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"KOL {request.kol_id} nano接口调用失败: {str(e)}")
        # 即使失败也要标记已调用，避免重复调用
        try:
            kol.update_nano_email_status(db, kol_id=request.kol_id, topics=[])
        except:
            pass

        raise HTTPException(status_code=500, detail=f"nano接口调用失败: {str(e)}")


@router.post("/start-batch-fetch-email", response_model=TaskStartResponse)
async def start_batch_fetch_email_from_nano(
    request: BatchNanoEmailRequest, background_tasks: BackgroundTasks
):
    """启动异步批量获取KOL邮箱任务"""
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 初始化任务状态
        now = datetime.now()
        task_status_store[task_id] = {
            "task_id": task_id,
            "status": "pending",
            "progress": 0,
            "total_count": len(request.kol_ids),
            "processed_count": 0,
            "success_count": 0,
            "failed_count": 0,
            "message": "任务已创建，等待开始",
            "results": None,
            "created_at": now,
            "updated_at": now,
        }

        # 添加后台任务
        background_tasks.add_task(
            _background_batch_fetch_email,
            task_id,
            request.kol_ids,
            request.force_update,
        )

        force_msg = "（强制更新模式）" if request.force_update else ""
        logger.info(
            f"启动批量邮箱获取任务: {task_id}, KOL数量: {len(request.kol_ids)}{force_msg}"
        )

        return TaskStartResponse(
            task_id=task_id,
            message=f"任务已启动，正在处理 {len(request.kol_ids)} 个KOL",
        )

    except Exception as e:
        logger.error(f"启动批量获取邮箱任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")


@router.get("/task-status/{task_id}", response_model=TaskStatusResponse)
async def get_batch_task_status(task_id: str):
    """获取批量任务状态"""
    if task_id not in task_status_store:
        raise HTTPException(status_code=404, detail="任务不存在")

    task_info = task_status_store[task_id]
    return TaskStatusResponse(**task_info)


@router.get("/status/{kol_id}")
def get_nano_status(kol_id: int, db: Session = Depends(get_db)):
    """获取KOL的nano接口调用状态"""
    db_kol = kol.get_by_kol_id(db, kol_id=kol_id)
    if not db_kol:
        raise HTTPException(status_code=404, detail="KOL不存在")

    return {
        "kol_id": kol_id,
        "nano_email_fetched_at": db_kol.nano_email_fetched_at,
        "nano_extracted_email": db_kol.nano_extracted_email,
        "ai_matched": db_kol.ai_matched,
        "email_fetch_status": db_kol.email_fetch_status,
    }


@router.get("/task-stats")
async def get_task_stats():
    """获取任务存储统计信息"""
    stats = get_task_store_stats(task_status_store)
    return {"nano_tasks": stats, "total_tasks_in_memory": len(task_status_store)}


@router.post("/cleanup-tasks")
async def cleanup_tasks(max_age_hours: int = 24):
    """清理过期任务"""
    cleaned_count = cleanup_old_tasks(task_status_store, max_age_hours)
    return {
        "message": f"清理了 {cleaned_count} 个过期任务",
        "cleaned_count": cleaned_count,
        "remaining_tasks": len(task_status_store),
    }


@router.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """删除指定任务"""
    success = force_cleanup_task(task_status_store, task_id)
    if success:
        return {"message": f"任务 {task_id} 已删除"}
    else:
        raise HTTPException(status_code=404, detail="任务不存在或删除失败")


async def _background_batch_fetch_email(
    task_id: str, kol_ids: List[int], force_update: bool = False
):
    """后台执行批量邮箱获取任务"""
    try:
        # 更新任务状态为运行中
        task_status_store[task_id].update(
            {
                "status": "running",
                "message": "正在处理中...",
                "updated_at": datetime.now(),
            }
        )

        force_msg = "（强制更新模式）" if force_update else ""
        logger.info(
            f"开始后台批量获取任务: {task_id}, KOL数量: {len(kol_ids)}{force_msg}"
        )

        # 使用受控并发处理
        results = await _batch_fetch_with_progress_update(
            task_id, kol_ids, force_update
        )

        # 统计结果
        success_count = sum(
            1 for r in results if isinstance(r, NanoEmailResponse) and r.success
        )
        failed_count = len(results) - success_count

        # 更新任务状态为完成
        task_status_store[task_id].update(
            {
                "status": "completed",
                "progress": 100,
                "processed_count": len(kol_ids),
                "success_count": success_count,
                "failed_count": failed_count,
                "message": f"任务完成: 成功 {success_count}, 失败 {failed_count}",
                "results": results,
                "updated_at": datetime.now(),
            }
        )

        logger.info(
            f"后台批量获取任务完成: {task_id}, 成功: {success_count}, 失败: {failed_count}"
        )

    except Exception as e:
        logger.error(f"后台批量获取任务失败: {task_id}, 错误: {str(e)}")
        task_status_store[task_id].update(
            {
                "status": "failed",
                "message": f"任务失败: {str(e)}",
                "updated_at": datetime.now(),
            }
        )


async def _batch_fetch_with_progress_update(
    task_id: str, kol_ids: List[int], force_update: bool = False
) -> List:
    """带进度更新的批量邮箱获取"""
    concurrency_semaphore = asyncio.Semaphore(5)
    processed_count = 0
    results = []

    async def controlled_fetch_with_progress(kol_id: int) -> NanoEmailResponse:
        nonlocal processed_count
        async with concurrency_semaphore:
            try:
                result = await _process_single_kol_async(kol_id, force_update)
                processed_count += 1

                # 更新进度
                progress = int((processed_count / len(kol_ids)) * 100)
                task_status_store[task_id].update(
                    {
                        "progress": progress,
                        "processed_count": processed_count,
                        "updated_at": datetime.now(),
                    }
                )

                return result
            except Exception as e:
                processed_count += 1
                logger.error(f"处理KOL {kol_id} 失败: {str(e)}")
                return NanoEmailResponse(
                    kol_id=kol_id,
                    email=None,
                    topics=[],
                    success=False,
                    message=f"处理失败: {str(e)}",
                )

    # 创建任务
    tasks = [controlled_fetch_with_progress(kol_id) for kol_id in kol_ids]

    # 执行并收集结果
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 处理异常结果
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            kol_id = kol_ids[i]
            error_response = NanoEmailResponse(
                kol_id=kol_id,
                email=None,
                topics=[],
                success=False,
                message=f"处理异常: {str(result)}",
            )
            processed_results.append(error_response)
        else:
            processed_results.append(result)

    return processed_results


async def _process_single_kol_async(
    kol_id: int, force_update: bool = False
) -> NanoEmailResponse:
    """异步处理单个KOL的nano邮箱获取"""
    # 创建新的数据库会话（线程安全）
    from app.db.session import SessionLocal

    db = SessionLocal()

    try:
        # 检查KOL是否存在
        db_kol = kol.get_by_kol_id(db, kol_id=kol_id)
        if not db_kol:
            return NanoEmailResponse(
                kol_id=kol_id, email=None, topics=[], success=False, message="KOL不存在"
            )

        # 检查是否已经调用过nano接口（除非强制更新）
        if db_kol.nano_email_fetched_at and not force_update:
            return NanoEmailResponse(
                kol_id=kol_id,
                email=db_kol.nano_extracted_email,
                topics=db_kol.topics or [],
                success=True,
                message="已调用过nano接口",
            )
        elif db_kol.nano_email_fetched_at and force_update:
            logger.info(f"KOL {kol_id} 强制更新模式，重新调用nano接口")

        # 检查KOL是否通过AI匹配
        if not db_kol.ai_matched:
            return NanoEmailResponse(
                kol_id=kol_id,
                email=None,
                topics=[],
                success=False,
                message="KOL未通过AI匹配，不允许调用nano接口",
            )

        # 构建KOL的URL
        kol_url = _build_kol_url(db_kol)
        if not kol_url:
            return NanoEmailResponse(
                kol_id=kol_id,
                email=None,
                topics=[],
                success=False,
                message="无法构建KOL的URL",
            )

        # 调用异步nano服务
        nano_service = NanoService()
        result = await nano_service.async_get_kol_email_by_url(kol_url)

        # 处理nano服务返回结果
        extracted_email = None
        topics = []
        if result and isinstance(result, dict):
            extracted_email = result.get("email")
            topics = result.get("topics", [])
        elif isinstance(result, str):
            extracted_email = result

        # 更新数据库状态
        kol.update_nano_email_status(
            db, kol_id=kol_id, extracted_email=extracted_email, topics=topics
        )

        return NanoEmailResponse(
            kol_id=kol_id,
            email=extracted_email,
            topics=topics,
            success=True,
            message="nano接口调用成功",
        )

    except Exception as e:
        logger.error(f"KOL {kol_id} nano接口调用失败: {str(e)}")
        # 即使失败也要标记已调用，避免重复调用
        try:
            kol.update_nano_email_status(db, kol_id=kol_id, topics=[])
        except:
            pass

        return NanoEmailResponse(
            kol_id=kol_id,
            email=None,
            topics=[],
            success=False,
            message=f"nano接口调用失败: {str(e)}",
        )
    finally:
        db.close()


def _build_kol_url(kol_obj) -> Optional[str]:
    """根据平台和social_id构建KOL的URL"""
    if kol_obj.platform.value == "TIKTOK":
        return f"https://www.tiktok.com/@{kol_obj.social_id}"
    elif kol_obj.platform.value == "INSTAGRAM":
        return f"https://www.instagram.com/{kol_obj.social_id}"
    elif kol_obj.platform.value == "YOUTUBE":
        return f"https://www.youtube.com/@{kol_obj.social_id}"
    else:
        return None
