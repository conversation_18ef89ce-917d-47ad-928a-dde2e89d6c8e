"""
邮件日志管理API端点
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import email_send_logs
from app.models.enums import EmailSendStatusEnum
from app.schemas.email_send_logs import (
    EmailSend,
    EmailSendCreate,
    EmailSendCreateInternal,
    EmailSendListResponse,
    EmailSendSearch,
    EmailSendStatsResponse,
    EmailSendStatusUpdate,
)

router = APIRouter()


def _convert_email_model_to_schema(email_model, kol_social_id: str) -> EmailSend:
    """将数据库模型转换为 API schema"""
    return EmailSend(
        id=email_model.id,
        platform=email_model.platform,
        social_id=kol_social_id,
        project_code=email_model.project_code,
        template_code=email_model.template_code,
        from_email=email_model.from_email,
        to_email=email_model.to_email,
        note=email_model.note,
        send_status=email_model.send_status,
        send_date=email_model.send_date,
        created_at=email_model.created_at,
        updated_at=email_model.updated_at,
    )


def _convert_emails_list_to_schema(db: Session, emails_list) -> List[EmailSend]:
    """批量转换邮件记录列表为 API schema"""
    from app.crud import kol

    converted_emails = []
    for email in emails_list:
        kol_obj = kol.get_by_kol_id(db, kol_id=email.kol_id)
        social_id = kol_obj.social_id if kol_obj else f"unknown_{email.kol_id}"
        converted_emails.append(_convert_email_model_to_schema(email, social_id))
    return converted_emails


@router.get("/", response_model=EmailSendListResponse)
def get_email_send_logs(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    kol_id: Optional[int] = Query(None, description="KOL ID"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    status: Optional[EmailSendStatusEnum] = Query(None, description="发送状态"),
    template_code: Optional[str] = Query(None, description="模板代码"),
    db: Session = Depends(get_db),
):
    """获取邮件发送记录列表"""

    if kol_id:
        emails = email_send_logs.get_by_kol(db, kol_id=kol_id, skip=skip, limit=limit)
        total = len(email_send_logs.get_by_kol(db, kol_id=kol_id, skip=0, limit=10000))
    elif project_code:
        emails = email_send_logs.get_by_project(
            db, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            email_send_logs.get_by_project(
                db, project_code=project_code, skip=0, limit=10000
            )
        )
    elif status:
        emails = email_send_logs.get_by_status(
            db, status=status, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            email_send_logs.get_by_status(
                db, status=status, project_code=project_code, skip=0, limit=10000
            )
        )
    elif template_code:
        emails = email_send_logs.get_by_template(
            db, template_code=template_code, skip=skip, limit=limit
        )
        total = len(
            email_send_logs.get_by_template(
                db, template_code=template_code, skip=0, limit=10000
            )
        )
    else:
        emails = email_send_logs.get_multi(db, skip=skip, limit=limit)
        total = len(email_send_logs.get_multi(db, skip=0, limit=10000))

    # 转换模型为 schema
    converted_emails = _convert_emails_list_to_schema(db, emails)

    return EmailSendListResponse(
        items=converted_emails, total=total, skip=skip, limit=limit
    )


@router.post("/search", response_model=EmailSendListResponse)
def search_email_send_logs(
    search_params: EmailSendSearch,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
):
    """搜索邮件发送记录"""
    # 如果提供了 social_id，需要先查找对应的 kol_id
    kol_id = None
    if search_params.social_id:
        from app.crud import kol

        # 这里需要平台信息来唯一确定KOL，如果没有提供平台则无法精确查找
        if search_params.platform and search_params.project_code:
            kol_obj = kol.get_by_social_id_platform_project(
                db,
                social_id=search_params.social_id,
                platform=search_params.platform,
                project_code=search_params.project_code,
            )
            if kol_obj:
                kol_id = kol_obj.id
        else:
            # 如果没有平台信息，只能模糊查找
            kol_obj = kol.get_by_social_id_only(db, social_id=search_params.social_id)
            if kol_obj:
                kol_id = kol_obj.id

    emails = email_send_logs.search_emails(
        db,
        kol_id=kol_id,
        platform=search_params.platform,
        status=search_params.status,
        template_code=search_params.template_code,
        project_code=search_params.project_code,
        from_email=search_params.from_email,
        to_email=search_params.to_email,
        skip=skip,
        limit=limit,
    )

    # 计算总数
    total = len(
        email_send_logs.search_emails(
            db,
            kol_id=kol_id,
            platform=search_params.platform,
            status=search_params.status,
            template_code=search_params.template_code,
            project_code=search_params.project_code,
            from_email=search_params.from_email,
            to_email=search_params.to_email,
            skip=0,
            limit=10000,
        )
    )

    # 转换模型为 schema
    converted_emails = _convert_emails_list_to_schema(db, emails)

    return EmailSendListResponse(
        items=converted_emails, total=total, skip=skip, limit=limit
    )


@router.get("/stats", response_model=EmailSendStatsResponse)
def get_email_send_stats(
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db),
):
    """获取邮件发送统计信息"""
    stats = email_send_logs.get_email_statistics(db, project_code=project_code)
    return EmailSendStatsResponse(**stats)


@router.get("/pending", response_model=List[EmailSend])
def get_pending_emails(
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
):
    """获取待发送的邮件"""
    return email_send_logs.get_pending_emails(db, limit=limit)


@router.get("/date-range", response_model=EmailSendListResponse)
def get_emails_by_date_range(
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
):
    """根据日期范围获取邮件发送记录"""
    emails = email_send_logs.get_by_date_range(
        db,
        start_date=start_date,
        end_date=end_date,
        project_code=project_code,
        skip=skip,
        limit=limit,
    )

    total = len(
        email_send_logs.get_by_date_range(
            db,
            start_date=start_date,
            end_date=end_date,
            project_code=project_code,
            skip=0,
            limit=10000,
        )
    )

    # 转换模型为 schema
    converted_emails = _convert_emails_list_to_schema(db, emails)

    return EmailSendListResponse(
        items=converted_emails, total=total, skip=skip, limit=limit
    )


@router.get("/{email_id}", response_model=EmailSend)
def get_email_send(email_id: int, db: Session = Depends(get_db)):
    """根据邮件ID获取邮件发送详情"""
    from app.crud import kol

    db_email = email_send_logs.get(db, id=email_id)
    if not db_email:
        raise HTTPException(status_code=404, detail="邮件发送记录不存在")

    # 查找 KOL 的 social_id
    kol_obj = kol.get_by_kol_id(db, kol_id=db_email.kol_id)
    social_id = kol_obj.social_id if kol_obj else f"unknown_{db_email.kol_id}"

    return _convert_email_model_to_schema(db_email, social_id)


@router.post("/", response_model=EmailSend)
def create_email_send(email_in: EmailSendCreate, db: Session = Depends(get_db)):
    """创建新邮件发送记录"""
    from app.crud import kol

    # 根据 social_id, platform, project_code 查找 KOL
    if not email_in.platform:
        raise HTTPException(status_code=400, detail="平台信息不能为空")

    kol_obj = kol.get_by_social_id_platform_project(
        db,
        social_id=email_in.social_id,
        platform=email_in.platform,
        project_code=email_in.project_code,
    )
    if not kol_obj:
        raise HTTPException(status_code=404, detail="未找到对应的KOL")

    # 创建邮件发送记录，使用 KOL 的主键 ID
    # 创建内部使用的 EmailSendCreateInternal 对象
    internal_email_data = EmailSendCreateInternal(
        platform=email_in.platform,
        kol_id=kol_obj.id,
        project_code=email_in.project_code,
        template_code=email_in.template_code,
        from_email=email_in.from_email,
        to_email=email_in.to_email,
        note=email_in.note,
    )

    created_email = email_send_logs.create_internal(db, obj_in=internal_email_data)

    # 返回时将 kol_id 替换为 social_id
    return _convert_email_model_to_schema(created_email, kol_obj.social_id)


@router.patch("/{email_id}/status", response_model=EmailSend)
def update_email_status(
    email_id: int, status_update: EmailSendStatusUpdate, db: Session = Depends(get_db)
):
    """更新邮件发送状态"""
    from app.crud import kol

    db_email = email_send_logs.update_send_status(
        db,
        email_id=email_id,
        status=status_update.status,
        send_date=status_update.send_date,
    )
    if not db_email:
        raise HTTPException(status_code=404, detail="邮件发送记录不存在")

    # 查找 KOL 的 social_id
    kol_obj = kol.get_by_kol_id(db, kol_id=db_email.kol_id)
    social_id = kol_obj.social_id if kol_obj else f"unknown_{db_email.kol_id}"

    return _convert_email_model_to_schema(db_email, social_id)


@router.delete("/{email_id}", response_model=EmailSend)
def delete_email_send(email_id: int, db: Session = Depends(get_db)):
    """删除邮件发送记录"""
    from app.crud import kol

    db_email = email_send_logs.get(db, id=email_id)
    if not db_email:
        raise HTTPException(status_code=404, detail="邮件发送记录不存在")

    # 查找 KOL 的 social_id
    kol_obj = kol.get_by_kol_id(db, kol_id=db_email.kol_id)
    social_id = kol_obj.social_id if kol_obj else f"unknown_{db_email.kol_id}"

    deleted_email = email_send_logs.remove(db, id=email_id)
    return _convert_email_model_to_schema(deleted_email, social_id)
