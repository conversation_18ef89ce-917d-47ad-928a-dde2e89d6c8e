"""
KOL表现视图API端点
"""

from decimal import Decimal
from typing import Optional

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import kol_performance_view
from app.models.enums import PlatformEnum
from app.schemas.kol_performance_view import (
    KOLPerformanceListResponse,
    KOLPerformanceSearch,
    KOLPerformanceView,
)

router = APIRouter()


@router.get("/", response_model=KOLPerformanceListResponse)
def get_kol_performance_data(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    platform: Optional[PlatformEnum] = Query(None, description="平台"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    status: Optional[str] = Query(None, description="绩效状态"),
    min_cpm: Optional[Decimal] = Query(None, description="最小CPM", ge=0),
    max_cpm: Optional[Decimal] = Query(None, description="最大CPM", ge=0),
    min_views: Optional[int] = Query(None, description="最小观看数", ge=0),
    max_views: Optional[int] = Query(None, description="最大观看数", ge=0),
    db: Session = Depends(get_db),
):
    """获取KOL表现数据列表"""

    # 获取数据
    items_data = kol_performance_view.get_kol_performance_view(
        db=db,
        skip=skip,
        limit=limit,
        platform=platform,
        project_code=project_code,
        status=status,
        min_cpm=min_cpm,
        max_cpm=max_cpm,
        min_views=min_views,
        max_views=max_views,
    )

    # 获取总数
    total = kol_performance_view.count_kol_performance_view(
        db=db,
        platform=platform,
        project_code=project_code,
        status=status,
        min_cpm=min_cpm,
        max_cpm=max_cpm,
        min_views=min_views,
        max_views=max_views,
    )

    # 转换为Pydantic模型
    items = [KOLPerformanceView(**item) for item in items_data]

    return KOLPerformanceListResponse(items=items, total=total, skip=skip, limit=limit)


@router.post("/search", response_model=KOLPerformanceListResponse)
def search_kol_performance_data(
    search_params: KOLPerformanceSearch,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
):
    """
    高级搜索KOL表现数据

    支持多种查询条件：
    - 基础筛选：平台、项目编码、状态
    - KOL信息搜索：social_id、nick_name（支持精确匹配和模糊搜索）
    - 帖子信息搜索：post_link（支持精确匹配和模糊搜索）
    - 数值范围筛选：CPM、观看数、点赞数、评论数、分享数、互动率、支付金额
    - 日期范围筛选：发布日期、创建时间
    - 特殊筛选：是否有支付记录、是否有互动数据
    - 排序：支持多字段排序，升序/降序
    """

    # 获取数据
    items_data = kol_performance_view.search_kol_performance_view(
        db=db, search_params=search_params, skip=skip, limit=limit
    )

    # 获取总数
    total = kol_performance_view.count_search_kol_performance_view(
        db=db, search_params=search_params
    )

    # 转换为Pydantic模型
    items = [KOLPerformanceView(**item) for item in items_data]

    return KOLPerformanceListResponse(items=items, total=total, skip=skip, limit=limit)
