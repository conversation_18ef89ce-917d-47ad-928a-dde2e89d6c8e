"""
WebSocket任务端点
用于实时推送爬虫任务进度
"""

from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import crawler_task
from app.logging_config import get_logger
from app.utils.concurrency_control import async_concurrency_limit
from app.websocket.task_manager import task_websocket_manager

logger = get_logger("websocket_tasks")
router = APIRouter()


@router.websocket("/ws/tasks/{task_id}")
@async_concurrency_limit
async def websocket_task_endpoint(
    websocket: WebSocket, task_id: int, db: Session = Depends(get_db)
):
    """
    WebSocket端点：实时推送任务进度

    连接URL: ws://domain/api/v1/ws/tasks/{task_id}
    """
    # 验证任务是否存在
    db_task = crawler_task.get(db, id=task_id)
    if not db_task:
        await websocket.close(code=4004, reason="Task not found")
        return

    try:
        # 建立WebSocket连接
        await task_websocket_manager.connect(task_id, websocket)
        logger.info(f"客户端连接到任务WebSocket: task_id={task_id}")

        # 发送当前任务状态
        await task_websocket_manager.send_progress_update(
            task_id=task_id,
            progress=db_task.task_progress,
            log_msg=db_task.log_msg,
            status=db_task.status.value,
            total_duration=(
                float(db_task.total_duration) if db_task.total_duration else None
            ),
        )

        # 保持连接活跃，等待消息
        while True:
            try:
                # 接收客户端消息（如果有的话）
                data = await websocket.receive_text()
                logger.debug(f"收到客户端消息: task_id={task_id}, data={data}")

                # 这里可以处理客户端发送的消息，比如请求当前状态
                if data == "get_status":
                    # 重新获取最新状态
                    db.refresh(db_task)
                    await task_websocket_manager.send_progress_update(
                        task_id=task_id,
                        progress=db_task.task_progress,
                        log_msg=db_task.log_msg,
                        status=db_task.status.value,
                        total_duration=(
                            float(db_task.total_duration)
                            if db_task.total_duration
                            else None
                        ),
                    )

            except WebSocketDisconnect:
                logger.info(f"客户端主动断开WebSocket连接: task_id={task_id}")
                break
            except Exception as e:
                logger.error(
                    f"WebSocket消息处理错误: task_id={task_id}, error={str(e)}"
                )
                break

    except Exception as e:
        logger.error(f"WebSocket连接错误: task_id={task_id}, error={str(e)}")
    finally:
        # 清理连接
        await task_websocket_manager.disconnect(task_id)


@router.get("/ws/tasks/{task_id}/status")
@async_concurrency_limit
async def get_websocket_status(task_id: int):
    """获取WebSocket连接状态"""
    is_connected = task_websocket_manager.is_connected(task_id)
    return {"task_id": task_id, "websocket_connected": is_connected}
