"""
爬虫任务管理API端点
"""

from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import crawler_task
from app.models.enums import CrawlerTaskStatusEnum, PlatformEnum
from app.schemas.crawler_task import (
    CrawlerTask,
    CrawlerTaskCreate,
    CrawlerTaskCreateResponse,
    CrawlerTaskListResponse,
    CrawlerTaskSearch,
    CrawlerTaskStatsResponse,
    CrawlerTaskUpdate,
)
from app.tasks.async_task_executor import async_task_executor
from app.tasks.steps.task_validation_step import Params<PERSON>hecker
from app.utils.concurrency_control import async_concurrency_limit

router = APIRouter()


@router.get("/", response_model=CrawlerTaskListResponse)
def get_crawler_tasks(
    # TODO : tasks按照创建时间降序排列
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    platform: Optional[PlatformEnum] = Query(None, description="爬取平台"),
    status: Optional[CrawlerTaskStatusEnum] = Query(None, description="任务状态"),
    db: Session = Depends(get_db),
):
    """获取爬虫任务列表"""
    if project_code:
        tasks = crawler_task.get_by_project(
            db, project_code=project_code, skip=skip, limit=limit
        )
        total = len(
            crawler_task.get_by_project(
                db, project_code=project_code, skip=0, limit=10000
            )
        )
    elif platform:
        tasks = crawler_task.get_by_platform(
            db, platform=platform, skip=skip, limit=limit
        )
        total = len(
            crawler_task.get_by_platform(db, platform=platform, skip=0, limit=10000)
        )
    elif status:
        tasks = crawler_task.get_by_status(db, status=status, skip=skip, limit=limit)
        total = len(crawler_task.get_by_status(db, status=status, skip=0, limit=10000))
    else:
        tasks = crawler_task.get_multi(db, skip=skip, limit=limit)
        total = len(crawler_task.get_multi(db, skip=0, limit=10000))

    return CrawlerTaskListResponse(items=tasks, total=total, skip=skip, limit=limit)


@router.post("/search", response_model=CrawlerTaskListResponse)
def search_crawler_tasks(
    search_params: CrawlerTaskSearch,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
):
    """搜索爬虫任务"""
    tasks = crawler_task.search_tasks(
        db,
        task_name=search_params.task_name,
        platform=search_params.platform,
        status=search_params.status,
        project_code=search_params.project_code,
        skip=skip,
        limit=limit,
    )

    # 计算总数
    total = len(
        crawler_task.search_tasks(
            db,
            task_name=search_params.task_name,
            platform=search_params.platform,
            status=search_params.status,
            project_code=search_params.project_code,
            skip=0,
            limit=10000,
        )
    )

    return CrawlerTaskListResponse(items=tasks, total=total, skip=skip, limit=limit)


@router.get("/stats", response_model=CrawlerTaskStatsResponse)
def get_crawler_task_stats(
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db),
):
    """获取爬虫任务统计信息"""
    stats = crawler_task.get_task_statistics(db, project_code=project_code)
    return CrawlerTaskStatsResponse(**stats)


@router.get("/{task_id}", response_model=CrawlerTask)
def get_crawler_task(task_id: int, db: Session = Depends(get_db)):
    """根据任务ID获取爬虫任务详情"""
    db_task = crawler_task.get(db, id=task_id)
    if not db_task:
        raise HTTPException(status_code=404, detail="爬虫任务不存在")
    return db_task


@router.post("/", response_model=CrawlerTaskCreateResponse)
@async_concurrency_limit
async def create_crawler_task(
    task_in: CrawlerTaskCreate, db: Session = Depends(get_db)
):
    """创建新爬虫任务并异步执行"""
    try:
        # 验证参数
        task_params = task_in.model_dump()
        ParamsChecker(**task_params)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"参数验证失败: {str(e)}")

    try:
        # 创建任务记录
        db_task = crawler_task.create(db, obj_in=task_in)
        task_id = db_task.id

        # 准备任务参数
        task_params = {
            "task_name": task_in.task_name,
            "source": task_in.source.value,
            "platform": task_in.platform.value,
            "project_code": task_in.project_code,
            "filters": task_in.filters or {},
            "cookies": task_in.cookies or "",
        }

        # 启动异步任务
        success = await async_task_executor.start_task(task_id, task_params)

        if not success:
            raise HTTPException(status_code=500, detail="任务启动失败")

        # 构建WebSocket URL
        websocket_url = f"/api/v1/ws/tasks/{task_id}"

        return CrawlerTaskCreateResponse(
            task_id=task_id,
            message="任务已创建并开始执行",
            status=CrawlerTaskStatusEnum.RUNNING,
            websocket_url=websocket_url,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")


@router.put("/{task_id}", response_model=CrawlerTask)
def update_crawler_task(
    task_id: int, task_in: CrawlerTaskUpdate, db: Session = Depends(get_db)
):
    """更新爬虫任务"""
    db_task = crawler_task.get(db, id=task_id)
    if not db_task:
        raise HTTPException(status_code=404, detail="爬虫任务不存在")
    return crawler_task.update(db, db_obj=db_task, obj_in=task_in)


@router.post("/{task_id}/cancel")
@async_concurrency_limit
async def cancel_task(task_id: int, db: Session = Depends(get_db)):
    """取消正在运行的任务"""
    # 检查任务是否存在
    db_task = crawler_task.get(db, id=task_id)
    if not db_task:
        raise HTTPException(status_code=404, detail="爬虫任务不存在")

    # 检查任务是否正在运行
    if not async_task_executor.is_task_running(task_id):
        # 如果任务不在运行队列中，但状态仍为RUNNING，则修复状态
        if db_task.status == CrawlerTaskStatusEnum.RUNNING:
            crawler_task.update_status(
                db,
                task_id=task_id,
                status=CrawlerTaskStatusEnum.FAILED,
                log_msg="任务状态不一致，已自动修复为失败状态",
            )
            return {"message": "任务状态不一致，已自动修复"}
        else:
            raise HTTPException(status_code=400, detail="任务未在运行中")

    # 取消任务
    success = await async_task_executor.cancel_task(task_id)

    if success:
        # 更新数据库状态为CANCELLED
        crawler_task.update_status(
            db,
            task_id=task_id,
            status=CrawlerTaskStatusEnum.CANCELLED,
            log_msg="任务已被用户取消",
        )
        return {"message": "任务已取消"}
    else:
        raise HTTPException(status_code=500, detail="取消任务失败")


@router.delete("/{task_id}", response_model=CrawlerTask)
def delete_crawler_task(task_id: int, db: Session = Depends(get_db)):
    """删除爬虫任务"""
    db_task = crawler_task.get(db, id=task_id)
    if not db_task:
        raise HTTPException(status_code=404, detail="爬虫任务不存在")
    return crawler_task.remove(db, id=task_id)


@router.get("/{task_id}/status/detailed")
def get_task_detailed_status(task_id: int, db: Session = Depends(get_db)):
    """获取任务的详细状态信息（包括信号量状态）"""
    # 检查任务是否存在
    db_task = crawler_task.get(db, id=task_id)
    if not db_task:
        raise HTTPException(status_code=404, detail="爬虫任务不存在")

    # 获取执行器状态信息
    executor_status = async_task_executor.get_task_status_info(task_id)

    return {
        "task_id": task_id,
        "database_status": db_task.status.value,
        "database_progress": db_task.task_progress,
        "last_updated": db_task.updated_at,
        "executor_status": executor_status,
        "message": "基于信号量的任务状态信息"
    }
