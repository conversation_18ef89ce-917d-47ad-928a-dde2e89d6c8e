"""
定时任务工具函数
"""

import logging
from datetime import datetime
from typing import Any, Dict, List

from croniter import croniter


def validate_cron_expression(cron_expr: str) -> Dict[str, Any]:
    """
    验证cron表达式的有效性

    Args:
        cron_expr: cron表达式

    Returns:
        验证结果
        {
            "valid": bool,
            "message": str,
            "next_runs": list  # 如果有效，返回接下来几次的执行时间
        }
    """
    try:
        if not cron_expr or not isinstance(cron_expr, str):
            return {
                "valid": False,
                "message": "cron表达式不能为空且必须是字符串",
                "next_runs": [],
            }

        # 使用croniter验证
        base_time = datetime.now()
        cron = croniter(cron_expr, base_time)

        # 获取接下来5次执行时间
        next_runs = []
        for _ in range(5):
            next_run = cron.get_next(datetime)
            next_runs.append(next_run.strftime("%Y-%m-%d %H:%M:%S"))

        return {"valid": True, "message": "cron表达式有效", "next_runs": next_runs}

    except Exception as e:
        return {
            "valid": False,
            "message": f"无效的cron表达式: {str(e)}",
            "next_runs": [],
        }


def get_common_cron_expressions() -> Dict[str, str]:
    """
    获取常用的cron表达式

    Returns:
        常用cron表达式字典
    """
    return {
        "每分钟": "* * * * *",
        "每5分钟": "*/5 * * * *",
        "每15分钟": "*/15 * * * *",
        "每30分钟": "*/30 * * * *",
        "每小时": "0 * * * *",
        "每2小时": "0 */2 * * *",
        "每6小时": "0 */6 * * *",
        "每12小时": "0 */12 * * *",
        "每天凌晨": "0 0 * * *",
        "每天早上8点": "0 8 * * *",
        "每天中午12点": "0 12 * * *",
        "每天下午6点": "0 18 * * *",
        "每天晚上9点": "0 21 * * *",
        "每天晚上10点": "0 22 * * *",
        "每周一早上9点": "0 9 * * 1",
        "每月1号凌晨": "0 0 1 * *",
        "工作日早上9点": "0 9 * * 1-5",
        "周末早上10点": "0 10 * * 6,0",
    }


def parse_cron_description(cron_expr: str) -> str:
    """
    将cron表达式转换为人类可读的描述

    Args:
        cron_expr: cron表达式

    Returns:
        人类可读的描述
    """
    common_expressions = get_common_cron_expressions()

    # 查找是否是常用表达式
    for desc, expr in common_expressions.items():
        if expr == cron_expr:
            return desc

    # 简单的解析逻辑（可以后续扩展为更复杂的解析）
    try:
        parts = cron_expr.split()
        if len(parts) == 5:
            minute, hour, day, month, weekday = parts

            # 简单的描述生成
            if minute == "0" and hour != "*":
                if hour.isdigit():
                    return f"每天{hour}点执行"
                elif "/" in hour:
                    interval = hour.split("/")[1]
                    return f"每{interval}小时执行"

            if minute != "*" and hour != "*":
                if minute.isdigit() and hour.isdigit():
                    return f"每天{hour}:{minute.zfill(2)}执行"

        return f"自定义表达式: {cron_expr}"

    except Exception:
        return f"表达式: {cron_expr}"


def get_task_execution_summary(executions: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    获取任务执行摘要统计

    Args:
        executions: 任务执行记录列表

    Returns:
        执行摘要统计
    """
    if not executions:
        return {
            "total_executions": 0,
            "success_count": 0,
            "failed_count": 0,
            "timeout_count": 0,
            "success_rate": 0.0,
            "average_duration": 0.0,
            "last_execution": None,
        }

    total = len(executions)
    success_count = sum(1 for e in executions if e.get("status") == "success")
    failed_count = sum(1 for e in executions if e.get("status") == "failed")
    timeout_count = sum(1 for e in executions if e.get("status") == "timeout")

    success_rate = (success_count / total * 100) if total > 0 else 0.0

    # 计算平均执行时间
    durations = []
    for execution in executions:
        start_time = execution.get("start_time")
        end_time = execution.get("end_time")
        if start_time and end_time:
            try:
                start = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
                end = datetime.fromisoformat(end_time.replace("Z", "+00:00"))
                duration = (end - start).total_seconds()
                durations.append(duration)
            except Exception:
                continue

    average_duration = sum(durations) / len(durations) if durations else 0.0

    # 最近一次执行
    last_execution = (
        max(executions, key=lambda x: x.get("start_time", "")) if executions else None
    )

    return {
        "total_executions": total,
        "success_count": success_count,
        "failed_count": failed_count,
        "timeout_count": timeout_count,
        "success_rate": round(success_rate, 2),
        "average_duration": round(average_duration, 2),
        "last_execution": last_execution,
    }


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """
    设置日志配置

    Args:
        log_level: 日志级别

    Returns:
        配置好的logger
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    logger = logging.getLogger("schedule")
    return logger


def create_default_config() -> Dict[str, Any]:
    """
    创建默认的调度器配置

    Returns:
        默认配置字典
    """
    return {
        "timezone": "Asia/Shanghai",
        "default_cron": "0 22 * * *",  # 每晚10点
        "default_timeout_minutes": 30,
        "default_max_retries": 1,
        "default_retry_interval_minutes": 5,
        "max_workers": 10,
        "log_level": "INFO",
    }
