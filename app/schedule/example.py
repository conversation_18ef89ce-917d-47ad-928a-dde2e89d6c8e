"""
定时任务框架使用示例
演示如何使用定时任务框架
"""

import logging
import time
from datetime import datetime

from app.schedule import TaskConfig, TaskScheduler
from app.schedule.api import init_schedule_api
from app.schedule.utils import setup_logging, validate_cron_expression


# 模拟一个Task类（类似app/tasks下的Task）
class ExampleTask:
    """示例任务类"""

    def __init__(self, task_name: str):
        self.task_name = task_name
        self.logger = logging.getLogger(f"task.{task_name}")

    def run(self):
        """任务执行方法"""
        self.logger.info("Task %s started at %s", self.task_name, datetime.now())

        # 模拟任务执行
        time.sleep(2)  # 模拟2秒的工作

        # 模拟可能的失败（10%概率）
        import random

        if random.random() < 0.1:
            raise Exception(f"Task {self.task_name} failed randomly")

        self.logger.info("Task %s completed at %s", self.task_name, datetime.now())
        return f"Task {self.task_name} completed successfully"


def main():
    """主函数，演示框架使用"""

    # 设置日志
    setup_logging("INFO")
    logger = logging.getLogger("example")

    logger.info("=== 定时任务框架使用示例 ===")

    # 1. 创建调度器
    scheduler = TaskScheduler(timezone="Asia/Shanghai")

    # 2. 初始化API
    api = init_schedule_api(scheduler)

    # 3. 创建示例任务
    task1 = ExampleTask("data_sync")
    task2 = ExampleTask("report_generation")

    # 4. 注册任务
    config1 = TaskConfig(
        task_id="data_sync",
        cron_expr="*/30 * * * *",  # 每30分钟执行一次
        timeout_minutes=10,
        max_retries=2,
    )

    config2 = TaskConfig(
        task_id="report_generation",
        cron_expr="0 22 * * *",  # 每晚10点执行
        timeout_minutes=30,
        max_retries=1,
    )

    scheduler.register_task("data_sync", task1, config1)
    scheduler.register_task("report_generation", task2, config2)

    # 5. 启动调度器
    scheduler.start()
    logger.info("调度器已启动")

    # 6. 演示API调用
    logger.info("\n=== API调用演示 ===")

    # 获取所有任务列表
    result = api.list_all_tasks()
    logger.info("任务列表: %s", result)

    # 验证cron表达式
    cron_validation = validate_cron_expression("0 9 * * 1-5")
    logger.info("Cron验证结果: %s", cron_validation)

    # 更新任务调度
    update_result = api.update_task_schedule(
        "data_sync", "*/15 * * * *"
    )  # 改为每15分钟
    logger.info("更新调度结果: %s", update_result)

    # 获取任务信息
    task_info = api.get_task_info("data_sync")
    logger.info("任务信息: %s", task_info)

    # 7. 运行一段时间后演示移除任务
    logger.info("\n=== 运行演示 ===")
    logger.info("调度器将运行60秒，然后演示移除任务...")

    try:
        time.sleep(60)  # 运行1分钟

        # 移除一个任务
        remove_result = api.remove_task("report_generation")
        logger.info("移除任务结果: %s", remove_result)

        # 再运行30秒
        time.sleep(30)

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")

    finally:
        # 8. 关闭调度器
        scheduler.shutdown()
        logger.info("调度器已关闭")


def demo_api_functions():
    """演示API函数的使用"""

    logger = logging.getLogger("api_demo")
    logger.info("=== API函数演示 ===")

    # 创建调度器和任务
    scheduler = TaskScheduler()
    api = init_schedule_api(scheduler)

    # 注册一个简单任务
    simple_task = ExampleTask("demo_task")
    scheduler.register_task("demo_task", simple_task)
    scheduler.start()

    try:
        # 演示各种API调用

        # 1. 获取任务信息
        print("\n1. 获取任务信息:")
        result = api.get_task_info("demo_task")
        print(f"结果: {result['success']}")
        print(f"消息: {result['message']}")

        # 2. 更新调度
        print("\n2. 更新任务调度:")
        result = api.update_task_schedule("demo_task", "0 */2 * * *")  # 每2小时
        print(f"结果: {result['success']}")
        print(f"消息: {result['message']}")

        # 3. 禁用任务
        print("\n3. 禁用任务:")
        result = api.disable_task("demo_task")
        print(f"结果: {result['success']}")
        print(f"消息: {result['message']}")

        # 4. 启用任务
        print("\n4. 启用任务:")
        result = api.enable_task("demo_task")
        print(f"结果: {result['success']}")
        print(f"消息: {result['message']}")

        # 5. 列出所有任务
        print("\n5. 列出所有任务:")
        result = api.list_all_tasks()
        print(f"结果: {result['success']}")
        print(f"任务数量: {result['data']['total_count']}")

        # 6. 移除任务
        print("\n6. 移除任务:")
        result = api.remove_task("demo_task")
        print(f"结果: {result['success']}")
        print(f"消息: {result['message']}")

    finally:
        scheduler.shutdown()


if __name__ == "__main__":
    # 运行主演示
    main()

    print("\n" + "=" * 50)

    # 运行API演示
    demo_api_functions()
