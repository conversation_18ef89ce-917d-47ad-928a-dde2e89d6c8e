"""
定时任务调度器
基于APScheduler实现的简单任务调度系统
"""

import logging
import threading
import uuid
from concurrent.futures import Future, ThreadPoolExecutor, TimeoutError
from datetime import datetime
from typing import Any, Callable, Dict, Optional

import pytz
from apscheduler.executors.pool import Thread<PERSON>oolExecutor as APSThreadPoolExecutor
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger

from .models import TaskConfig, TaskExecution, TaskStatus


class TaskScheduler:
    """定时任务调度器"""

    def __init__(self, timezone: str = "Asia/Shanghai"):
        """
        初始化调度器

        Args:
            timezone: 时区，默认北京时间
        """
        self.timezone = pytz.timezone(timezone)
        self.logger = logging.getLogger(__name__)

        # 配置APScheduler
        jobstores = {"default": MemoryJobStore()}
        executors = {"default": APSThreadPoolExecutor(20)}
        job_defaults = {"coalesce": False, "max_instances": 1, "misfire_grace_time": 30}

        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone=self.timezone,
        )

        # 内存存储（后续可扩展为数据库）
        self.task_configs: Dict[str, TaskConfig] = {}
        self.task_executions: Dict[str, TaskExecution] = {}
        self.running_tasks: Dict[str, Future] = {}
        self.task_functions: Dict[str, Callable] = {}

        # 线程池用于任务执行和超时控制
        self.executor = ThreadPoolExecutor(max_workers=10)

        self.logger.info("TaskScheduler initialized with timezone: %s", timezone)

    def start(self):
        """启动调度器"""
        if not self.scheduler.running:
            self.scheduler.start()
            self.logger.info("TaskScheduler started")

    def shutdown(self, wait: bool = True):
        """关闭调度器"""
        if self.scheduler.running:
            self.scheduler.shutdown(wait=wait)
            self.executor.shutdown(wait=wait)
            self.logger.info("TaskScheduler shutdown")

    def register_task(
        self, task_id: str, task_function: Callable, config: Optional[TaskConfig] = None
    ):
        """
        注册任务

        Args:
            task_id: 任务ID
            task_function: 任务执行函数（应该有run()方法的Task对象）
            config: 任务配置，如果为None则使用默认配置
        """
        if config is None:
            config = TaskConfig(task_id=task_id)

        self.task_configs[task_id] = config
        self.task_functions[task_id] = task_function

        # 添加到调度器
        if config.enabled:
            self._add_job(task_id, config)

        self.logger.info("Task registered: %s with cron: %s", task_id, config.cron_expr)

    def _add_job(self, task_id: str, config: TaskConfig):
        """添加任务到调度器"""
        try:
            trigger = CronTrigger.from_crontab(config.cron_expr, timezone=self.timezone)
            self.scheduler.add_job(
                func=self._execute_task_wrapper,
                trigger=trigger,
                args=[task_id],
                id=task_id,
                replace_existing=True,
                name=f"Task-{task_id}",
            )
            self.logger.info("Job added to scheduler: %s", task_id)
        except Exception as e:
            self.logger.error("Failed to add job %s: %s", task_id, str(e))
            raise

    def _execute_task_wrapper(self, task_id: str):
        """任务执行包装器，处理超时和重试逻辑"""
        execution_id = str(uuid.uuid4())
        execution = TaskExecution(
            task_id=task_id,
            execution_id=execution_id,
            status=TaskStatus.PENDING,
            start_time=datetime.now(),
        )

        self.task_executions[execution_id] = execution
        self.logger.info("Task execution started: %s [%s]", task_id, execution_id)

        try:
            self._execute_with_timeout_and_retry(task_id, execution)
        except Exception as e:
            self.logger.error("Unexpected error in task execution: %s", str(e))
            execution.status = TaskStatus.FAILED
            execution.error_message = str(e)
            execution.end_time = datetime.now()

    def _execute_with_timeout_and_retry(self, task_id: str, execution: TaskExecution):
        """执行任务，包含超时控制和重试逻辑"""
        config = self.task_configs[task_id]
        task_function = self.task_functions[task_id]

        for attempt in range(config.max_retries + 1):
            if attempt > 0:
                execution.retry_count = attempt
                self.logger.info("Retrying task %s, attempt %d", task_id, attempt + 1)
                # 等待重试间隔
                threading.Event().wait(config.retry_interval_minutes * 60)

            execution.status = TaskStatus.RUNNING
            execution.start_time = datetime.now()

            try:
                # 提交任务到线程池执行
                future = self.executor.submit(self._run_task, task_function)
                self.running_tasks[execution.execution_id] = future

                # 等待任务完成或超时
                result = future.result(timeout=config.timeout_minutes * 60)

                # 任务成功完成
                execution.status = TaskStatus.SUCCESS
                execution.end_time = datetime.now()
                self.logger.info(
                    "Task completed successfully: %s [%s]",
                    task_id,
                    execution.execution_id,
                )
                break

            except TimeoutError:
                # 任务超时
                execution.status = TaskStatus.TIMEOUT
                execution.error_message = (
                    f"Task timeout after {config.timeout_minutes} minutes"
                )
                execution.end_time = datetime.now()
                self.logger.warning(
                    "Task timeout: %s [%s]", task_id, execution.execution_id
                )

                # 尝试取消任务
                future.cancel()

            except Exception as e:
                # 任务执行失败
                execution.status = TaskStatus.FAILED
                execution.error_message = str(e)
                execution.end_time = datetime.now()
                self.logger.error(
                    "Task failed: %s [%s] - %s", task_id, execution.execution_id, str(e)
                )

            finally:
                # 清理运行中的任务记录
                self.running_tasks.pop(execution.execution_id, None)

            # 如果是最后一次尝试，不再重试
            if attempt == config.max_retries:
                break

    def _run_task(self, task_function: Callable):
        """运行任务函数"""
        if hasattr(task_function, "run"):
            # 如果是Task对象，调用run方法
            return task_function.run()
        else:
            # 如果是普通函数，直接调用
            return task_function()

    def set_task_cron(self, task_id: str, cron_expr: str) -> bool:
        """
        修改任务的cron表达式

        Args:
            task_id: 任务ID
            cron_expr: 新的cron表达式

        Returns:
            bool: 是否修改成功
        """
        try:
            if task_id not in self.task_configs:
                self.logger.error("Task not found: %s", task_id)
                return False

            # 验证cron表达式
            CronTrigger.from_crontab(cron_expr, timezone=self.timezone)

            # 更新配置
            config = self.task_configs[task_id]
            old_cron = config.cron_expr
            config.cron_expr = cron_expr

            # 重新添加任务到调度器
            if config.enabled:
                self._add_job(task_id, config)

            self.logger.info(
                "Task cron updated: %s from '%s' to '%s'", task_id, old_cron, cron_expr
            )

            # TODO: 后续实现：将配置存入数据库，重启后读取
            # self.save_config_to_db(task_id, config)

            return True

        except Exception as e:
            self.logger.error("Failed to update task cron %s: %s", task_id, str(e))
            return False

    def remove_task(self, task_id: str) -> bool:
        """
        移除任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否移除成功
        """
        try:
            if task_id not in self.task_configs:
                self.logger.error("Task not found: %s", task_id)
                return False

            # 从调度器中移除任务
            if self.scheduler.get_job(task_id):
                self.scheduler.remove_job(task_id)

            # 终止正在运行的任务实例
            for execution_id, future in list(self.running_tasks.items()):
                execution = self.task_executions.get(execution_id)
                if execution and execution.task_id == task_id:
                    future.cancel()
                    execution.status = TaskStatus.CANCELLED
                    execution.end_time = datetime.now()
                    self.running_tasks.pop(execution_id, None)
                    self.logger.info(
                        "Cancelled running task: %s [%s]", task_id, execution_id
                    )

            # 清理配置和函数
            self.task_configs.pop(task_id, None)
            self.task_functions.pop(task_id, None)

            self.logger.info("Task removed: %s", task_id)
            return True

        except Exception as e:
            self.logger.error("Failed to remove task %s: %s", task_id, str(e))
            return False

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态信息，如果任务不存在返回None
        """
        if task_id not in self.task_configs:
            return None

        config = self.task_configs[task_id]

        # 获取最近的执行记录
        recent_executions = [
            exec for exec in self.task_executions.values() if exec.task_id == task_id
        ]
        recent_executions.sort(key=lambda x: x.start_time or datetime.min, reverse=True)

        last_execution = recent_executions[0] if recent_executions else None

        # 检查是否有正在运行的实例
        is_running = any(
            exec.task_id == task_id and exec.status == TaskStatus.RUNNING
            for exec in self.task_executions.values()
        )

        return {
            "task_id": task_id,
            "config": config.to_dict(),
            "is_running": is_running,
            "last_execution": last_execution.to_dict() if last_execution else None,
            "next_run_time": (
                self.scheduler.get_job(task_id).next_run_time.isoformat()
                if self.scheduler.get_job(task_id)
                else None
            ),
        }

    def list_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有任务及其状态

        Returns:
            所有任务的状态信息
        """
        return {
            task_id: self.get_task_status(task_id)
            for task_id in self.task_configs.keys()
        }

    # TODO: 后续拓展方向
    def save_config_to_db(self, task_id: str, config: TaskConfig):
        """
        后续实现：将配置存入数据库，重启后读取

        Args:
            task_id: 任务ID
            config: 任务配置
        """
        # TODO: 实现数据库持久化

    def load_configs_from_db(self):
        """
        后续实现：从数据库加载配置
        """
        # TODO: 实现从数据库加载配置
