"""
候选人相关的Pydantic schemas
"""

from datetime import datetime
from typing import Any, Dict, Optional, Union

from pydantic import BaseModel, EmailStr, Field

from app.models.enums import FollowUpStatusEnum, PlatformEnum


class CandidateBase(BaseModel):
    """候选人基础schema"""

    platform: Optional[PlatformEnum] = Field(None, description="KOL所在平台")
    kol_id: Optional[Union[int, str]] = Field(
        None, description="KOL ID（外键，可为空）或UUID字符串（需要审核时）"
    )
    social_id: Optional[str] = Field(
        None, description="KOL的社交媒体ID", max_length=255
    )
    nick_name: Optional[str] = Field(None, description="KOL的昵称", max_length=255)
    project_code: str = Field(..., description="项目编码", max_length=50)
    reply_email_addr: Optional[str] = Field(None, description="回复邮箱地址")
    follow_up_status: Optional[FollowUpStatusEnum] = Field(None, description="跟进状态")
    follow_up_note: Optional[str] = Field(None, description="跟进备注")
    first_contact_date: Optional[datetime] = Field(None, description="首次联系时间")
    last_contact_date: Optional[datetime] = Field(None, description="最近一次联系时间")
    send_round: int = Field(0, description="发送轮次", ge=0, le=100)
    latest_email_send_id: Optional[int] = Field(None, description="最新邮件发送ID")
    thread_id: str = Field(..., description="关联邮件线程ID", max_length=255)
    tracker: Optional[str] = Field(None, description="分配给的跟进人", max_length=50)
    note: Optional[str] = Field(None, description="备注")
    # Gmail同步相关字段
    parsed_email: Optional[Dict[str, Any]] = Field(
        None, description="解析的原始邮箱数据"
    )
    parsed_social_link: Optional[Dict[str, Any]] = Field(
        None, description="解析的原始社交媒体链接数据"
    )
    need_review: bool = Field(False, description="是否需要人工审核")


class CandidateCreate(CandidateBase):
    """创建候选人schema"""


class CandidateUpdate(BaseModel):
    """更新候选人schema"""

    platform: Optional[PlatformEnum] = Field(None, description="KOL所在平台")
    kol_id: Optional[Union[int, str]] = Field(
        None, description="KOL ID（外键，可为空）或UUID字符串（需要审核时）"
    )
    social_id: Optional[str] = Field(
        None, description="KOL的社交媒体ID", max_length=255
    )
    nick_name: Optional[str] = Field(None, description="KOL的昵称", max_length=255)
    project_code: Optional[str] = Field(None, description="项目编码", max_length=50)
    reply_email_addr: Optional[EmailStr] = Field(None, description="回复邮箱地址")
    follow_up_status: Optional[FollowUpStatusEnum] = Field(None, description="跟进状态")
    follow_up_note: Optional[str] = Field(None, description="跟进备注")
    send_round: Optional[int] = Field(None, description="发送轮次", ge=0, le=100)
    latest_email_send_id: Optional[int] = Field(None, description="最新邮件发送ID")
    tracker: Optional[str] = Field(None, description="分配给的跟进人", max_length=50)
    note: Optional[str] = Field(None, description="备注")
    parsed_email: Optional[Dict[str, Any]] = Field(
        None, description="解析的原始邮箱数据"
    )
    parsed_social_link: Optional[Dict[str, Any]] = Field(
        None, description="解析的原始社交媒体链接数据"
    )
    need_review: Optional[bool] = Field(None, description="是否需要人工审核")


class CandidateInDBBase(CandidateBase):
    """数据库中的候选人基础schema"""

    id: int
    follow_up_status: Optional[FollowUpStatusEnum]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Candidate(CandidateInDBBase):
    """返回给客户端的候选人schema"""


class CandidateInDB(CandidateInDBBase):
    """数据库中的候选人schema（内部使用）"""


# 候选人状态更新
class CandidateStatusUpdate(BaseModel):
    """候选人状态更新schema"""

    status: FollowUpStatusEnum = Field(..., description="新的跟进状态")
    note: Optional[str] = Field(None, description="跟进备注")


# 候选人分配跟进人
class CandidateTrackerAssign(BaseModel):
    """候选人分配跟进人schema"""

    tracker: str = Field(..., description="跟进人", max_length=50)


# 候选人搜索
class CandidateSearch(BaseModel):
    """候选人搜索schema"""

    nick_name: Optional[str] = Field(None, description="昵称（模糊匹配）")
    social_id: Optional[str] = Field(None, description="社交媒体ID（模糊匹配）")
    platform: Optional[PlatformEnum] = Field(None, description="平台")
    status: Optional[FollowUpStatusEnum] = Field(None, description="跟进状态")
    tracker: Optional[str] = Field(None, description="跟进人（模糊匹配）")
    project_code: Optional[str] = Field(None, description="项目编码")
    reply_email: Optional[str] = Field(None, description="回复邮箱（模糊匹配）")
    need_review: Optional[bool] = Field(None, description="是否需要人工审核")


# 候选人列表响应
class CandidateListResponse(BaseModel):
    """候选人列表响应schema"""

    items: list[Candidate]
    total: int
    skip: int
    limit: int


# 候选人统计响应
class CandidateStatsResponse(BaseModel):
    """候选人统计响应schema"""

    total: int
    status_stats: dict
    platform_stats: dict
    tracker_stats: dict
