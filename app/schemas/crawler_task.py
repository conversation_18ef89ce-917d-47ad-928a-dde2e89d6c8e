"""
爬虫任务相关的Pydantic schemas
"""

from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field

from app.models.enums import CrawlerTaskStatusEnum, PlatformEnum, SourceEnum


class CrawlerTaskBase(BaseModel):
    """爬虫任务基础schema"""

    task_name: str = Field(..., description="任务名称", max_length=255)
    source: SourceEnum = Field(..., description="数据源")
    platform: PlatformEnum = Field(..., description="爬取平台")
    project_code: str = Field(..., description="项目编码", max_length=50)
    filters: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="过滤条件JSON"
    )
    cookies: Optional[str] = Field(None, description="爬虫cookie")


class CrawlerTaskCreate(CrawlerTaskBase):
    """创建爬虫任务schema"""


class CrawlerTaskUpdate(BaseModel):
    """更新爬虫任务schema"""

    task_name: Optional[str] = Field(None, description="任务名称", max_length=255)
    source: Optional[SourceEnum] = Field(None, description="数据源")
    platform: Optional[PlatformEnum] = Field(None, description="爬取平台")
    status: Optional[CrawlerTaskStatusEnum] = Field(None, description="任务状态")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件JSON")
    cookies: Optional[str] = Field(None, description="爬虫cookie")
    log_msg: Optional[str] = Field(None, description="日志信息")


class CrawlerTaskInDBBase(CrawlerTaskBase):
    """数据库中的爬虫任务基础schema"""

    id: int
    status: CrawlerTaskStatusEnum
    log_msg: Optional[str]
    total_duration: Optional[float] = Field(None, description="任务总体耗时（秒）")
    task_progress: int = Field(0, ge=0, le=100, description="任务进度（0-100）")
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CrawlerTask(CrawlerTaskInDBBase):
    """返回给客户端的爬虫任务schema"""


class CrawlerTaskInDB(CrawlerTaskInDBBase):
    """数据库中的爬虫任务schema（内部使用）"""


# 爬虫任务状态更新
class CrawlerTaskStatusUpdate(BaseModel):
    """爬虫任务状态更新schema"""

    status: CrawlerTaskStatusEnum = Field(..., description="新状态")
    log_msg: Optional[str] = Field(None, description="日志信息")


# 爬虫任务进度更新
class CrawlerTaskProgressUpdate(BaseModel):
    """爬虫任务进度更新schema"""

    task_progress: int = Field(..., ge=0, le=100, description="任务进度（0-100）")
    log_msg: Optional[str] = Field(None, description="日志信息")
    status: Optional[CrawlerTaskStatusEnum] = Field(None, description="任务状态")
    total_duration: Optional[float] = Field(None, description="任务总体耗时（秒）")


# 爬虫任务搜索
class CrawlerTaskSearch(BaseModel):
    """爬虫任务搜索schema"""

    task_name: Optional[str] = Field(None, description="任务名称（模糊匹配）")
    platform: Optional[PlatformEnum] = Field(None, description="爬取平台")
    status: Optional[CrawlerTaskStatusEnum] = Field(None, description="任务状态")
    project_code: Optional[str] = Field(None, description="项目编码")


# 爬虫任务列表响应
class CrawlerTaskListResponse(BaseModel):
    """爬虫任务列表响应schema"""

    items: list[CrawlerTask]
    total: int
    skip: int
    limit: int


# 爬虫任务统计响应
class CrawlerTaskStatsResponse(BaseModel):
    """爬虫任务统计响应schema"""

    total: int
    pending: int
    running: int
    completed: int
    failed: int
    cancelled: int


# WebSocket消息schema
class WebSocketTaskMessage(BaseModel):
    """WebSocket任务消息schema"""

    task_id: int = Field(..., description="任务ID")
    task_progress: int = Field(..., ge=0, le=100, description="任务进度（0-100）")
    log_msg: Optional[str] = Field(None, description="日志信息")
    status: CrawlerTaskStatusEnum = Field(..., description="任务状态")
    total_duration: Optional[float] = Field(None, description="任务总体耗时（秒）")


# 任务创建响应
class CrawlerTaskCreateResponse(BaseModel):
    """爬虫任务创建响应schema"""

    task_id: int = Field(..., description="任务ID")
    message: str = Field(..., description="创建结果消息")
    status: CrawlerTaskStatusEnum = Field(..., description="任务状态")
    websocket_url: str = Field(..., description="WebSocket连接URL")
