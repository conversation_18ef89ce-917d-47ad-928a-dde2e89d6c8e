"""
绩效相关的Pydantic schemas
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from app.models.enums import PlatformEnum


class PerformanceBase(BaseModel):
    """绩效基础schema"""

    platform: PlatformEnum = Field(..., description="KOL所在平台")
    social_id: str = Field(..., description="KOL的社交媒体ID", max_length=255)
    kol_id: Optional[int] = Field(
        None,
        description="KOL ID（可选，不传时根据platform+social_id+project_code查找）",
    )
    project_code: str = Field(..., description="项目编码", max_length=50)

    post_link: str = Field(..., description="帖子链接", max_length=500)
    post_date: Optional[datetime] = Field(None, description="发布日期")

    # 总数据
    views_total: Optional[int] = Field(None, description="总观看数", ge=0)
    likes_total: Optional[int] = Field(None, description="总点赞数", ge=0)
    comments_total: Optional[int] = Field(None, description="总评论数", ge=0)
    shares_total: Optional[int] = Field(None, description="总分享数", ge=0)

    # 第一天数据
    views_day1: Optional[int] = Field(None, description="第一天观看数", ge=0)
    likes_day1: Optional[int] = Field(None, description="第一天点赞数", ge=0)
    comments_day1: Optional[int] = Field(None, description="第一天评论数", ge=0)
    shares_day1: Optional[int] = Field(None, description="第一天分享数", ge=0)

    # 第三天数据
    views_day3: Optional[int] = Field(None, description="第三天观看数", ge=0)
    likes_day3: Optional[int] = Field(None, description="第三天点赞数", ge=0)
    comments_day3: Optional[int] = Field(None, description="第三天评论数", ge=0)
    shares_day3: Optional[int] = Field(None, description="第三天分享数", ge=0)

    # 第七天数据
    views_day7: Optional[int] = Field(None, description="第七天观看数", ge=0)
    likes_day7: Optional[int] = Field(None, description="第七天点赞数", ge=0)
    comments_day7: Optional[int] = Field(None, description="第七天评论数", ge=0)
    shares_day7: Optional[int] = Field(None, description="第七天分享数", ge=0)


class PerformanceCreate(PerformanceBase):
    """创建绩效schema"""


class PerformanceUpdate(BaseModel):
    """更新绩效schema"""

    post_date: Optional[datetime] = Field(None, description="发布日期")

    # 总数据
    views_total: Optional[int] = Field(None, description="总观看数", ge=0)
    likes_total: Optional[int] = Field(None, description="总点赞数", ge=0)
    comments_total: Optional[int] = Field(None, description="总评论数", ge=0)
    shares_total: Optional[int] = Field(None, description="总分享数", ge=0)

    # 第一天数据
    views_day1: Optional[int] = Field(None, description="第一天观看数", ge=0)
    likes_day1: Optional[int] = Field(None, description="第一天点赞数", ge=0)
    comments_day1: Optional[int] = Field(None, description="第一天评论数", ge=0)
    shares_day1: Optional[int] = Field(None, description="第一天分享数", ge=0)

    # 第三天数据
    views_day3: Optional[int] = Field(None, description="第三天观看数", ge=0)
    likes_day3: Optional[int] = Field(None, description="第三天点赞数", ge=0)
    comments_day3: Optional[int] = Field(None, description="第三天评论数", ge=0)
    shares_day3: Optional[int] = Field(None, description="第三天分享数", ge=0)

    # 第七天数据
    views_day7: Optional[int] = Field(None, description="第七天观看数", ge=0)
    likes_day7: Optional[int] = Field(None, description="第七天点赞数", ge=0)
    comments_day7: Optional[int] = Field(None, description="第七天评论数", ge=0)
    shares_day7: Optional[int] = Field(None, description="第七天分享数", ge=0)


class PerformanceInDBBase(PerformanceBase):
    """数据库中的绩效基础schema"""

    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Performance(PerformanceInDBBase):
    """返回给客户端的绩效schema"""


class PerformanceInDB(PerformanceInDBBase):
    """数据库中的绩效schema（内部使用）"""


# 绩效搜索
class PerformanceSearch(BaseModel):
    """绩效搜索schema"""

    kol_id: Optional[int] = Field(None, description="KOL ID")
    platform: Optional[PlatformEnum] = Field(None, description="平台")
    project_code: Optional[str] = Field(None, description="项目编码")
    min_views: Optional[int] = Field(None, description="最小观看数", ge=0)
    max_views: Optional[int] = Field(None, description="最大观看数", ge=0)


# 绩效列表响应
class PerformanceListResponse(BaseModel):
    """绩效列表响应schema"""

    items: List[Performance]
    total: int
    skip: int
    limit: int


# 绩效统计响应
class PerformanceStatsResponse(BaseModel):
    """绩效统计响应schema"""

    total: int
    platform_stats: dict
