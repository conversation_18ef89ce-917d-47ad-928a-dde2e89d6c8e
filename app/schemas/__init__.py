# V2 Schema imports - 基于新的数据模型
from app.schemas.candidate import *
from app.schemas.crawler_task import *
from app.schemas.email_send_logs import *
from app.schemas.email_template import *
from app.schemas.kol import *
from app.schemas.kol_performance_view import *
from app.schemas.payment import *
from app.schemas.performance import *
from app.schemas.project import *

# 存在的Legacy Schema imports
from app.schemas.task import KOLDataTask, TaskBase, TaskCreate, TaskStatus

# 便于导入使用
__all__ = [
    "TaskBase",
    "TaskCreate",
    "TaskStatus",
    "KOLDataTask",
]
