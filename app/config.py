import os
from typing import Dict, List, Union

from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用配置类"""

    # 项目基本信息
    PROJECT_NAME: str = "KOL 数据管理与分析平台"
    API_V1_STR: str = "/api/v1"
    VERSION: str = "2.1.0"

    # 爬取配置
    MODASH_FIRST_PAGE_NUM: int = 0
    MODASH_PAGE_NUM: int = 0

    # 反爬虫配置
    SCRAPING_ENABLE_ANTI_DETECTION: bool = True
    SCRAPING_MIN_DELAY: float = 0.5
    SCRAPING_MAX_DELAY: float = 1.0
    SCRAPING_MAX_CONCURRENT: int = 5
    SCRAPING_MAX_RETRIES: int = 3
    SCRAPING_RETRY_DELAY: float = 2.0
    SCRAPING_TIMEOUT: int = 30
    SCRAPING_USER_AGENT_ROTATION: bool = True

    # User-Agent 列表配置
    SCRAPING_USER_AGENTS: List[str] = [
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    ]

    # 通用请求头配置
    SCRAPING_COMMON_HEADERS: Dict[str, str] = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Cache-Control": "max-age=0",
    }
    # CORS配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",") if i.strip()]
        elif isinstance(v, list):
            return v
        elif isinstance(v, str):
            return [v]
        return []

    # 数据库配置
    POSTGRES_SERVER: str = "127.0.0.1"
    POSTGRES_USER: str = "apple"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "kol_management"
    POSTGRES_PORT: int = 5432
    DATABASE_URI: str = (
        "postgresql+psycopg2://apple:postgres@127.0.0.1:5432/kol_management"
    )

    # TikHub API配置
    TIKHUB_TOKEN: str = "xacqaFZO6SIXljVf6mKMul9v/SEVtrzmuotC+SzipUZ5r1dVYtduzyCZUw=="
    TIKHUB_INSTAGRAM_USER_INFO_URL: str = (
        "https://api.tikhub.io/api/v1/instagram/web_app/fetch_user_info_by_user_id"
    )
    TIKHUB_MAX_CONCURRENT: int = 8  # TikHub API最大并发数
    # 异步并发控制配置
    ASYNC_MAX_CONCURRENCY: int = 10  # 异步接口最大并发数

    # 文件上传配置
    UPLOAD_DIR: str = "uploads"  # 文件上传目录
    PAYMENT_SCREENSHOT_DIR: str = "payment_screenshots"  # 支付截图目录
    MAX_FILE_SIZE: int = 5 * 1024 * 1024  # 最大文件大小 5MB
    ALLOWED_FILE_EXTENSIONS: List[str] = [
        ".jpg",
        ".jpeg",
        ".png",
        ".pdf",
    ]  # 允许的文件扩展名

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE_PATH: str = "logs"
    LOG_FILE_NAME: str = "kol_platform.log"
    LOG_MAX_BYTES: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5  # 保留5个备份文件

    @property
    def log_file(self) -> str:
        """获取完整的日志文件路径"""
        os.makedirs(self.LOG_FILE_PATH, exist_ok=True)
        return os.path.join(self.LOG_FILE_PATH, self.LOG_FILE_NAME)

    @property
    def payment_screenshot_path(self) -> str:
        """获取支付截图存储路径"""
        full_path = os.path.join(self.UPLOAD_DIR, self.PAYMENT_SCREENSHOT_DIR)
        os.makedirs(full_path, exist_ok=True)
        return full_path

    model_config = SettingsConfigDict(
        case_sensitive=True,
        extra="ignore",
        env_file=".env",
        env_file_encoding="utf-8",
    )


# 创建全局设置实例
settings = Settings()
