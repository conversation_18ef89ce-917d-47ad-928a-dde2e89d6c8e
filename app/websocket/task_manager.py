"""
WebSocket任务管理器
用于管理爬虫任务的WebSocket连接和消息推送
"""

import asyncio
import json
from typing import Dict

from fastapi import WebSocket

from app.logging_config import get_logger
from app.schemas.crawler_task import WebSocketTaskMessage

logger = get_logger("websocket_task_manager")


class TaskWebSocketManager:
    """任务WebSocket连接管理器"""

    def __init__(self):
        # 存储任务ID与WebSocket连接的映射
        self.connections: Dict[int, WebSocket] = {}
        # 存储连接锁，防止并发问题
        self._locks: Dict[int, asyncio.Lock] = {}

    async def connect(self, task_id: int, websocket: WebSocket):
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            self.connections[task_id] = websocket
            self._locks[task_id] = asyncio.Lock()
            logger.info(f"WebSocket连接已建立: task_id={task_id}")
        except Exception as e:
            logger.error(f"WebSocket连接失败: task_id={task_id}, error={str(e)}")
            raise

    async def disconnect(self, task_id: int):
        """断开WebSocket连接"""
        if task_id in self.connections:
            try:
                websocket = self.connections[task_id]
                await websocket.close()
            except Exception as e:
                logger.warning(
                    f"关闭WebSocket连接时出错: task_id={task_id}, error={str(e)}"
                )
            finally:
                del self.connections[task_id]
                if task_id in self._locks:
                    del self._locks[task_id]
                logger.info(f"WebSocket连接已断开: task_id={task_id}")

    async def send_message(self, task_id: int, message: WebSocketTaskMessage):
        """向指定任务发送消息"""
        if task_id not in self.connections:
            logger.warning(f"任务WebSocket连接不存在: task_id={task_id}")
            return False

        websocket = self.connections[task_id]
        lock = self._locks.get(task_id)

        if not lock:
            logger.warning(f"任务锁不存在: task_id={task_id}")
            return False

        try:
            async with lock:
                message_dict = message.dict()
                await websocket.send_text(json.dumps(message_dict))
                logger.info(
                    f"WebSocket消息已发送: task_id={task_id}, progress={message.task_progress}"
                )
                return True
        except Exception as e:
            logger.error(f"WebSocket消息发送失败: task_id={task_id}, error={str(e)}")
            # 连接可能已断开，清理连接
            await self.disconnect(task_id)
            return False

    async def send_progress_update(
        self,
        task_id: int,
        progress: int,
        log_msg: str = None,
        status: str = "RUNNING",
        total_duration: float = None,
    ):
        """发送进度更新消息"""
        message = WebSocketTaskMessage(
            task_id=task_id,
            task_progress=progress,
            log_msg=log_msg,
            status=status,
            total_duration=total_duration,
        )
        return await self.send_message(task_id, message)

    async def send_completion_message(
        self,
        task_id: int,
        success: bool = True,
        log_msg: str = None,
        total_duration: float = None,
    ):
        """发送任务完成消息"""
        status = "COMPLETED" if success else "FAILED"
        progress = 100 if success else None

        message = WebSocketTaskMessage(
            task_id=task_id,
            task_progress=progress or 0,
            log_msg=log_msg,
            status=status,
            total_duration=total_duration,
        )

        # 发送完成消息
        result = await self.send_message(task_id, message)

        # 延迟断开连接，给前端时间处理消息
        if result:
            await asyncio.sleep(1)
            await self.disconnect(task_id)

        return result

    def is_connected(self, task_id: int) -> bool:
        """检查任务是否有活跃的WebSocket连接"""
        return task_id in self.connections

    async def cleanup_disconnected(self):
        """清理已断开的连接"""
        disconnected_tasks = []

        for task_id, websocket in self.connections.items():
            try:
                # 尝试发送ping消息检查连接状态
                await websocket.ping()
            except Exception:
                disconnected_tasks.append(task_id)

        for task_id in disconnected_tasks:
            await self.disconnect(task_id)

        if disconnected_tasks:
            logger.info(f"清理了{len(disconnected_tasks)}个断开的WebSocket连接")


# 全局WebSocket管理器实例
task_websocket_manager = TaskWebSocketManager()
