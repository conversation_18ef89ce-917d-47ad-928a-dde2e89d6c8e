"""
异步任务执行器 - 基于信号量的取消机制
用于异步执行爬虫任务并实时推送进度，无需埋点检查
"""

import asyncio
import time
from typing import Any, Dict

from sqlalchemy.orm import Session

from app.crud import crawler_task
from app.db.session import SessionLocal
from app.logging_config import get_logger
from app.models.enums import CrawlerTaskStatusEnum
from app.tasks.step import TaskCancelledException, CancellationSignal
from app.tasks.task import ModashCrawlerTask
from app.websocket.task_manager import task_websocket_manager

logger = get_logger("async_task_executor")


class AsyncTaskExecutor:
    """异步任务执行器 - 基于信号量的取消机制，无需埋点检查"""

    def __init__(self):
        self.running_tasks: Dict[int, asyncio.Task] = {}
        self.cancellation_signals: Dict[int, CancellationSignal] = {}

    async def execute_crawler_task(self, task_id: int, task_params: Dict[str, Any]):
        """异步执行爬虫任务 - 基于信号量的取消机制"""
        start_time = time.time()
        db = SessionLocal()

        # 创建取消信号量
        cancellation_signal = CancellationSignal()
        self.cancellation_signals[task_id] = cancellation_signal

        try:
            logger.info(f"开始执行异步爬虫任务: task_id={task_id}")

            # 更新任务状态为运行中
            await self._update_task_progress(
                db,
                task_id,
                0,
                "任务开始执行",
                CrawlerTaskStatusEnum.RUNNING,
                append_log=False,
            )

            # 创建ModashCrawlerTask实例
            shared_data = {"validated_params": task_params, "crawler_task_id": task_id}
            crawler = ModashCrawlerTask(shared_data)

            # 执行任务的各个步骤（使用信号量监控取消）
            await self._execute_task_steps_with_cancellation(crawler, task_id, db)

            # 计算总耗时
            total_duration = time.time() - start_time

            # 任务完成
            await self._update_task_progress(
                db,
                task_id,
                100,
                "✅ 任务执行完成",
                CrawlerTaskStatusEnum.COMPLETED,
                total_duration,
                append_log=True,
            )

            # 发送WebSocket完成消息
            await task_websocket_manager.send_completion_message(
                task_id, True, "✅ 任务执行完成", total_duration
            )

            logger.info(
                f"✅ 异步爬虫任务执行完成: task_id={task_id}, duration={total_duration:.2f}s"
            )

        except (asyncio.CancelledError, TaskCancelledException):
            # 任务被取消
            total_duration = time.time() - start_time
            cancel_msg = "任务已被用户取消"

            logger.info(f"异步爬虫任务被取消: task_id={task_id}")

            # 检查是否确实被信号量取消
            if task_id in self.cancellation_signals and self.cancellation_signals[task_id].is_cancelled():
                await self._update_task_progress(
                    db,
                    task_id,
                    None,
                    cancel_msg,
                    CrawlerTaskStatusEnum.CANCELLED,
                    total_duration,
                    append_log=True,
                )

                # 发送WebSocket取消消息
                await task_websocket_manager.send_completion_message(
                    task_id, False, cancel_msg, total_duration
                )

            # 重新抛出异常以确保任务正确结束
            raise

        except Exception as e:
            # 计算耗时
            total_duration = time.time() - start_time
            error_msg = f"❌ 任务执行失败: {str(e)}"
            
            import traceback
            logger.error(
                f"❌ 异步爬虫任务执行失败: task_id={task_id}, error={str(e)}\n{traceback.format_exc()}"
            )

            # 只有在任务未被取消时才更新为失败状态
            if task_id not in self.cancellation_signals or not self.cancellation_signals[task_id].is_cancelled():
                await self._update_task_progress(
                    db,
                    task_id,
                    None,
                    error_msg,
                    CrawlerTaskStatusEnum.FAILED,
                    total_duration,
                    append_log=True,
                )

                # 发送WebSocket失败消息
                await task_websocket_manager.send_completion_message(
                    task_id, False, error_msg, total_duration
                )

        finally:
            db.close()
            # 清理运行中的任务记录
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            # 清理取消信号量
            if task_id in self.cancellation_signals:
                del self.cancellation_signals[task_id]

    async def _execute_task_steps_with_cancellation(
        self, crawler: ModashCrawlerTask, task_id: int, db: Session
    ):
        """执行任务步骤 - 使用信号量监控取消，无需埋点"""
        total_steps = len(crawler.steps)
        cancellation_signal = self.cancellation_signals[task_id]

        for i, step_class in enumerate(crawler.steps):
            # 检查取消信号
            if cancellation_signal.is_cancelled():
                logger.info(f"任务 {task_id} 在步骤 {step_class.__name__} 开始前被取消")
                raise TaskCancelledException(
                    task_id, f"Task cancelled before step {step_class.__name__}"
                )

            step_progress = int((i / total_steps) * 90)  # 预留10%给最终处理
            step_name = step_class.__name__

            logger.info(f"执行步骤: {step_name} (task_id={task_id})")

            # 更新进度
            await self._update_task_progress(
                db, task_id, step_progress, f"正在执行: {step_name}"
            )

            # 执行步骤（使用竞争执行模式）
            if crawler.shared_data is None:
                crawler.shared_data = {}
            crawler.shared_data.update({"task_id": task_id})
            step_instance = step_class(crawler.shared_data)

            try:
                # 使用竞争执行：步骤执行 vs 取消信号
                await self._execute_step_with_cancellation_monitoring(
                    step_instance, task_id, cancellation_signal
                )
                logger.info(f"步骤完成: {step_name} (task_id={task_id})")
            except TaskCancelledException:
                logger.info(f"步骤 {step_name} 被取消 (task_id={task_id})")
                raise

        # 最终处理前检查取消状态
        if cancellation_signal.is_cancelled():
            logger.info(f"任务 {task_id} 在最终处理前被取消")
            raise TaskCancelledException(task_id, "Task cancelled before final processing")

        # 最终处理
        await self._update_task_progress(db, task_id, 95, "正在完成最终处理...")

    async def _execute_step_with_cancellation_monitoring(
        self, step_instance, task_id: int, cancellation_signal: CancellationSignal
    ):
        """执行单个步骤，同时监控取消信号"""
        
        async def step_runner():
            """步骤执行器"""
            return await asyncio.to_thread(step_instance.run_with_log)
        
        async def cancellation_monitor():
            """取消信号监控器"""
            while not cancellation_signal.is_cancelled():
                await asyncio.sleep(0.1)  # 100ms检查间隔
            raise TaskCancelledException(task_id, "Task cancelled during step execution")
        
        # 竞争执行：步骤完成 vs 取消信号
        done, pending = await asyncio.wait(
            [step_runner(), cancellation_monitor()],
            return_when=asyncio.FIRST_COMPLETED
        )
        
        # 取消未完成的任务
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        # 获取结果或抛出异常
        completed_task = done.pop()
        return await completed_task

    async def _update_task_progress(
        self,
        db: Session,
        task_id: int,
        progress: int,
        log_msg: str,
        status: CrawlerTaskStatusEnum = None,
        total_duration: float = None,
        append_log: bool = True,
    ):
        """更新任务进度"""
        try:
            # 更新数据库
            update_data = {"task_progress": progress}
            if status:
                update_data["status"] = status
            if total_duration is not None:
                update_data["total_duration"] = total_duration

            # 更新日志
            if log_msg:
                if append_log:
                    crawler_task.update_status(
                        db, task_id=task_id, status=status, log_msg=log_msg, append_log=True
                    )
                else:
                    crawler_task.update_status(
                        db, task_id=task_id, status=status, log_msg=log_msg, append_log=False
                    )

            # 发送WebSocket进度更新
            await task_websocket_manager.send_progress_update(
                task_id=task_id,
                progress=progress,
                log_msg=log_msg,
                status=status.value if status else None,
                total_duration=total_duration,
            )

        except Exception as e:
            logger.error(f"更新任务进度失败: task_id={task_id}, error={str(e)}")

    async def start_task(self, task_id: int, task_params: Dict[str, Any]):
        """启动异步任务"""
        if task_id in self.running_tasks:
            logger.warning(f"任务已在运行中: task_id={task_id}")
            return False

        # 创建异步任务
        task = asyncio.create_task(self.execute_crawler_task(task_id, task_params))
        self.running_tasks[task_id] = task

        logger.info(f"异步任务已启动: task_id={task_id}")
        return True

    def is_task_running(self, task_id: int) -> bool:
        """检查任务是否正在运行"""
        return task_id in self.running_tasks and not self.running_tasks[task_id].done()

    def is_task_cancelled(self, task_id: int) -> bool:
        """检查任务是否已被取消"""
        return (
            task_id in self.cancellation_signals
            and self.cancellation_signals[task_id].is_cancelled()
        )

    def get_running_task_ids(self) -> list:
        """获取所有正在运行的任务ID"""
        return list(self.running_tasks.keys())

    def get_cancelled_task_ids(self) -> list:
        """获取所有已取消的任务ID"""
        return [
            task_id for task_id, signal in self.cancellation_signals.items()
            if signal.is_cancelled()
        ]

    async def cancel_task(self, task_id: int) -> bool:
        """取消正在运行的任务"""
        if task_id not in self.running_tasks:
            logger.warning(f"任务 {task_id} 不在运行队列中，无法取消")
            return False

        logger.info(f"开始取消任务: task_id={task_id}")

        # 发送取消信号
        if task_id in self.cancellation_signals:
            self.cancellation_signals[task_id].cancel()
            logger.info(f"已发送取消信号: task_id={task_id}")

        # 取消asyncio任务
        task = self.running_tasks[task_id]
        task.cancel()

        try:
            # 等待任务完成取消
            await asyncio.wait_for(task, timeout=30.0)  # 30秒超时
        except asyncio.CancelledError:
            logger.info(f"任务已成功取消: task_id={task_id}")
        except asyncio.TimeoutError:
            logger.warning(f"任务取消超时: task_id={task_id}")
        except TaskCancelledException:
            logger.info(f"任务通过信号量被取消: task_id={task_id}")
        except Exception as e:
            logger.error(f"任务取消过程中出现异常: task_id={task_id}, error={str(e)}")

        return True

    def get_task_status_info(self, task_id: int) -> dict:
        """获取任务的详细状态信息"""
        return {
            "task_id": task_id,
            "is_running": self.is_task_running(task_id),
            "is_cancelled": self.is_task_cancelled(task_id),
            "has_cancellation_signal": task_id in self.cancellation_signals,
        }


# 全局实例
async_task_executor = AsyncTaskExecutor()
