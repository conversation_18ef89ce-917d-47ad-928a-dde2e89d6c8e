"""
HTTP请求处理步骤
提供通用的HTTP请求功能，支持重试机制和反爬虫功能
"""

import asyncio
import random
import time
from typing import Dict, List, Optional

import aiohttp
import requests

from app.config import settings
from app.logging_config import get_logger
from app.tasks.step import Step
from app.utils.concurrency_control import AntiDetectionConfig

logger = get_logger("http_request_step")


class HttpRequestStep(Step):
    """通用HTTP请求步骤"""

    def __init__(self, shared_data=...):
        super().__init__(shared_data)

    def run(self):
        """执行HTTP请求"""
        url = self.get_input("http_request_params").get("url")
        method = self.get_input("http_request_params").get("method") or "GET"
        headers = self.get_input("http_request_params").get("headers") or {}
        data = self.get_input("http_request_params").get("data")
        json_data = self.get_input("http_request_params").get("json")
        params = self.get_input("http_request_params").get("params")
        timeout = self.get_input("http_request_params").get("timeout") or 30
        max_retries = self.get_input("http_request_params").get("max_retries") or 3
        retry_delay = self.get_input("http_request_params").get("retry_delay") or 2

        if not url:
            raise ValueError("URL is required for HTTP request")

        for attempt in range(max_retries):
            try:
                logger.info(f"Making {method} request to {url} (attempt {attempt + 1})")

                response = requests.request(
                    method=method,
                    url=url,
                    headers=headers,
                    data=data,
                    json=json_data,
                    params=params,
                    timeout=timeout,
                )

                if response.status_code == 200:
                    result = {
                        "status_code": response.status_code,
                        "headers": dict(response.headers),
                        "content": response.text,
                        "json": None,
                    }

                    # 尝试解析JSON
                    try:
                        result["json"] = response.json()
                    except:
                        pass

                    self.set_output("response", result)
                    logger.info(f"HTTP request successful: {response.status_code}")
                    return result
                else:
                    logger.warning(
                        f"HTTP request failed with status {response.status_code}"
                    )
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        raise requests.RequestException(
                            f"HTTP {response.status_code}: {response.text}"
                        )

            except Exception as e:
                logger.error(f"HTTP request attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    raise

        raise Exception(f"HTTP request failed after {max_retries} attempts")


class AntiDetectHttpRequestStep(Step):
    """增强的HTTP请求步骤，支持反爬虫功能"""

    def __init__(self, shared_data):
        super().__init__(shared_data)
        self.session = None

    def _create_session(self) -> requests.Session:
        """创建带有反爬虫配置的会话"""
        session = requests.Session()

        # 设置连接池参数
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10, pool_maxsize=20, max_retries=0  # 我们自己处理重试
        )
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    def _add_random_delay(self, custom_delay: Optional[float] = None):
        """添加随机延迟"""
        if custom_delay is not None:
            delay = custom_delay
        else:
            delay = AntiDetectionConfig.get_random_delay()

        logger.debug(f"Adding delay: {delay:.2f} seconds")
        time.sleep(delay)

    def _should_retry(self, status_code: int, attempt: int, max_retries: int) -> bool:
        """判断是否应该重试"""
        if attempt >= max_retries - 1:
            return False

        # 对于这些状态码进行重试
        retry_codes = [429, 500, 502, 503, 504, 520, 521, 522, 523, 524]
        return status_code in retry_codes

    def run(self):
        """执行HTTP请求"""
        params = self.get_input("http_request_params")
        if not params:
            raise ValueError("http_request_params is required")

        url = params.get("url")
        method = params.get("method", "POST")
        custom_headers = params.get("headers", {})
        data = params.get("data")
        json_data = params.get("json")
        query_params = params.get("params")
        timeout = params.get("timeout", AntiDetectionConfig.get_timeout())
        max_retries = params.get("max_retries", AntiDetectionConfig.get_max_retries())
        retry_delay = params.get("retry_delay", AntiDetectionConfig.get_retry_delay())
        enable_anti_detection = params.get(
            "enable_anti_detection", AntiDetectionConfig.is_anti_detection_enabled()
        )
        custom_delay = params.get("delay")

        if not url:
            raise ValueError("URL is required for HTTP request")

        # 创建会话
        if not self.session:
            self.session = self._create_session()

        # 构建请求头
        if enable_anti_detection:
            headers = AntiDetectionConfig.build_headers(custom_headers)
        else:
            headers = custom_headers.copy() if custom_headers else {}

        for attempt in range(max_retries):
            try:
                # 添加随机延迟（除了第一次请求）
                if attempt > 0 or enable_anti_detection:
                    self._add_random_delay(custom_delay)

                logger.info(
                    f"Making {method} request to {url} (attempt {attempt + 1}/{max_retries})"
                )
                logger.debug(f"Headers: {headers}")

                response = self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    data=data,
                    json=json_data,
                    params=query_params,
                    timeout=timeout,
                    allow_redirects=True,
                )

                logger.info(f"Response status: {response.status_code}")

                # 检查响应状态
                if response.status_code == 200:
                    result = {
                        "status_code": response.status_code,
                        "headers": dict(response.headers),
                        "content": response.text,
                        "json": None,
                        "url": response.url,
                        "cookies": dict(response.cookies),
                    }

                    # 尝试解析JSON
                    try:
                        if response.headers.get("content-type", "").startswith(
                            "application/json"
                        ):
                            result["json"] = response.json()
                    except Exception as e:
                        logger.debug(f"Failed to parse JSON: {e}")

                    self.set_output("response", result)

                    logger.info(f"HTTP request successful: {response.status_code}")
                    return result
                elif self._should_retry(response.status_code, attempt, max_retries):
                    logger.warning(
                        f"HTTP request failed with status {response.status_code}, retrying..."
                    )

                    # 对于429状态码，增加额外延迟
                    # 从响应头中获取重试时间
                    extra_delay = random.uniform(5, 10)
                    retry_after = response.headers.get("Retry-After")
                    if retry_after and response.status_code == 429:
                        try:
                            extra_delay = int(retry_after)
                        except ValueError:
                            pass
                    time.sleep(retry_delay)
                    logger.info(f"Rate limited, adding extra delay: {extra_delay:.2f}s")
                    continue
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                    logger.error(error_msg)
                    raise requests.RequestException(error_msg)

            except requests.exceptions.Timeout as e:
                logger.error(f"Request timeout on attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    raise

            except requests.exceptions.ConnectionError as e:
                logger.error(f"Connection error on attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay * 2)  # 连接错误时延迟更长
                    continue
                else:
                    raise

            except Exception as e:
                logger.error(f"HTTP request attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    raise

        raise Exception(f"HTTP request failed after {max_retries} attempts")

    def __del__(self):
        """清理会话"""
        if self.session:
            self.session.close()


class AsyncHttpRequestStep(Step):
    """异步HTTP请求步骤，用于并发爬取"""

    def __init__(self, shared_data):
        super().__init__(shared_data)
        self.semaphore = asyncio.Semaphore(8)
        self.connector = None

    async def _create_session(self, max_concurrent: int = 5) -> "aiohttp.ClientSession":
        # 创建连接器，限制并发连接数
        self.connector = aiohttp.TCPConnector(
            limit=max_concurrent * 2,
            limit_per_host=max_concurrent,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )

        # 创建信号量控制并发数
        self.semaphore = asyncio.Semaphore(max_concurrent)

        # 设置超时
        timeout = aiohttp.ClientTimeout(
            total=AntiDetectionConfig.get_timeout(), connect=10
        )

        return aiohttp.ClientSession(
            connector=self.connector,
            timeout=timeout,
            headers=settings.SCRAPING_COMMON_HEADERS,
        )

    async def _make_single_request(
        self, session: "aiohttp.ClientSession", request_params: Dict
    ) -> Dict:
        """执行单个异步请求"""
        async with self.semaphore:
            url = request_params.get("url")
            method = request_params.get("method", "POST")
            headers = request_params.get("headers", {})
            data = request_params.get("data")
            json_data = request_params.get("json")
            params = request_params.get("params")
            delay = request_params.get("delay")

            # 添加随机延迟
            if delay:
                await asyncio.sleep(delay)
            else:
                await asyncio.sleep(AntiDetectionConfig.get_random_delay())

            # 构建请求头
            request_headers = AntiDetectionConfig.build_headers(headers)

            try:
                logger.info(f"Making async {method} request to {url}")

                async with session.request(
                    method=method,
                    url=url,
                    headers=request_headers,
                    data=data,
                    json=json_data,
                    params=params,
                ) as response:

                    content = await response.text()

                    result = {
                        "status_code": response.status,
                        "headers": dict(response.headers),
                        "content": content,
                        "json": None,
                        "url": str(response.url),
                        "request_params": request_params,
                    }

                    # 尝试解析JSON
                    if response.content_type == "application/json":
                        try:
                            result["json"] = await response.json()
                        except Exception as e:
                            logger.debug(f"Failed to parse JSON: {e}")

                    logger.info(f"Async request completed: {response.status}")
                    return result

            except Exception as e:
                logger.error(f"Async request failed for {url}: {str(e)}")
                return {
                    "status_code": 0,
                    "error": str(e),
                    "url": url,
                    "request_params": request_params,
                }

    async def run_async(
        self, requests_list: List[Dict], max_concurrent: int = 5
    ) -> List[Dict]:
        """执行多个异步请求"""
        if not requests_list:
            return []

        session = await self._create_session(max_concurrent)

        try:
            # 创建任务列表
            tasks = [
                self._make_single_request(session, request_params)
                for request_params in requests_list
            ]

            # 并发执行所有请求
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Request {i} failed with exception: {result}")
                    processed_results.append(
                        {
                            "status_code": 0,
                            "error": str(result),
                            "request_params": requests_list[i],
                        }
                    )
                else:
                    processed_results.append(result)

            return processed_results

        finally:
            await session.close()
            if self.connector:
                await self.connector.close()

    def run(self):
        """同步接口，运行异步请求"""
        # 开始前检查取消状态
        self.check_cancellation()

        requests_list = self.get_input("requests_list")
        max_concurrent = self.get_input("max_concurrent") or 5

        if not requests_list:
            raise ValueError("requests_list is required for async HTTP requests")

        # 运行异步任务前再次检查取消状态
        self.check_cancellation()

        # 运行异步任务
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        results = loop.run_until_complete(self.run_async(requests_list, max_concurrent))

        # 异步任务完成后检查取消状态
        self.check_cancellation()

        self.set_output("responses", results)
        return results
