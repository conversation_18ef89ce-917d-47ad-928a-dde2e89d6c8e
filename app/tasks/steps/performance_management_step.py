"""
绩效数据管理步骤
管理KOL绩效数据的创建、更新和查询
"""

import asyncio
from typing import Dict, List, Optional

from sqlalchemy import select

from app.db.session import get_async_db, get_db  # get_db 临时保留，待其他类异步化后移除
from app.logging_config import get_logger
from app.models.performance import Performance
from app.tasks.step import Step

logger = get_logger("performance_management_step")


class GetPerformanceRecordsStep(Step):
    """获取绩效记录步骤 - 全异步批量获取"""

    def run(self):
        """获取需要更新的绩效记录（全异步，高性能）"""
        batch_size = self.get_input("batch_size") or 1000  # 默认每批1000条
        project_code = self.get_input("project_code")

        # 使用异步方法获取数据
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        post_links_data = loop.run_until_complete(
            self._async_get_performance_records(batch_size, project_code)
        )

        self.set_output("post_links_data", post_links_data)

        logger.info(f"获取绩效记录: {len(post_links_data)} 条")

        return {"post_links_data": post_links_data, "count": len(post_links_data)}

    async def _async_get_performance_records(
        self, batch_size: int, project_code: Optional[str] = None
    ) -> List[Dict]:
        """异步获取绩效记录的完整信息"""
        all_records = []
        skip = 0

        async for db in get_async_db():
            try:
                while True:
                    # 查询所需的字段：platform, kol_id, project_code, post_link
                    query = select(
                        Performance.platform,
                        Performance.kol_id,
                        Performance.project_code,
                        Performance.post_link,
                    ).filter(Performance.post_link.isnot(None))

                    # 如果指定了项目代码，则过滤
                    if project_code:
                        query = query.filter(Performance.project_code == project_code)

                    # 使用分页查询
                    query = query.offset(skip).limit(batch_size)

                    result = await db.execute(query)
                    records = result.fetchall()

                    if not records:
                        break

                    # 转换为字典格式
                    for record in records:
                        record_data = {
                            "platform": (
                                record.platform.value if record.platform else None
                            ),
                            "kol_id": record.kol_id,
                            "social_id": "",  # 暂时为空，因为数据库表中没有这个字段
                            "project_code": record.project_code,
                            "post_link": record.post_link,
                        }
                        all_records.append(record_data)

                    skip += batch_size

                    # 如果返回的记录数少于批大小，说明已经获取完毕
                    if len(records) < batch_size:
                        break

                break  # 成功获取数据后退出

            except Exception as e:
                logger.error(f"异步获取绩效记录失败: {str(e)}")
                raise

        return all_records
