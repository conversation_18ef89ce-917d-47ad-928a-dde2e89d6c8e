import asyncio
import random
from typing import Any, Dict, List, Optional

import aiohttp

from app.api.deps import get_db
from app.config import settings
from app.crud.crawler_task import crawler_task
from app.logging_config import get_logger
from app.models.enums import CrawlerTaskStatusEnum, PlatformEnum
from app.tasks.step import Step
from app.tasks.steps.http_request_step import AsyncHttpRequestStep
from app.utils.concurrency_control import AntiDetectionConfig
from app.utils.tools import email_format

logger = get_logger("ModashSearchStep")


class AsyncAntiDetectHttpRequestStep(Step):
    """异步增强的HTTP请求步骤，支持反爬虫功能"""

    def __init__(self, shared_data):
        super().__init__(shared_data)
        self.connector = None

    async def _create_session(self) -> "aiohttp.ClientSession":
        """创建带有反爬虫配置的异步会话"""
        # 创建连接器
        self.connector = aiohttp.TCPConnector(
            limit=20,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )

        # 设置超时
        timeout = aiohttp.ClientTimeout(
            total=AntiDetectionConfig.get_timeout(), connect=10
        )

        return aiohttp.ClientSession(connector=self.connector, timeout=timeout)

    async def _add_random_delay(self, custom_delay: Optional[float] = None):
        """添加异步随机延迟"""
        if custom_delay is not None:
            delay = custom_delay
        else:
            delay = AntiDetectionConfig.get_random_delay()

        logger.info(f"Adding async delay: {delay:.2f} seconds")
        await asyncio.sleep(delay)

    def _should_retry(self, status_code: int, attempt: int, max_retries: int) -> bool:
        """判断是否应该重试"""
        if attempt >= max_retries - 1:
            return False

        # 对于这些状态码进行重试
        retry_codes = [429, 500, 502, 503, 504, 520, 521, 522, 523, 524]
        return status_code in retry_codes

    async def run_async(self) -> Dict:
        """执行异步HTTP请求"""
        params = self.get_input("http_request_params")
        if not params:
            raise ValueError("http_request_params is required")

        url = params.get("url")
        method = params.get("method", "POST")
        custom_headers = params.get("headers", {})
        data = params.get("data")
        json_data = params.get("json")
        query_params = params.get("params")
        max_retries = params.get("max_retries", AntiDetectionConfig.get_max_retries())
        retry_delay = params.get("retry_delay", AntiDetectionConfig.get_retry_delay())
        enable_anti_detection = params.get(
            "enable_anti_detection", AntiDetectionConfig.is_anti_detection_enabled()
        )
        custom_delay = params.get("delay")

        if not url:
            raise ValueError("URL is required for HTTP request")

        # 确保 url 是字符串类型
        url = str(url)

        # 创建会话
        session = await self._create_session()

        try:
            # 构建请求头
            if enable_anti_detection:
                headers = AntiDetectionConfig.build_headers(custom_headers)
            else:
                headers = custom_headers.copy() if custom_headers else {}

            for attempt in range(max_retries):
                try:
                    # 添加随机延迟（除了第一次请求）
                    if attempt > 0 or enable_anti_detection:
                        await self._add_random_delay(custom_delay)

                    logger.info(
                        f"Making async {method} request to {url} (attempt {attempt + 1}/{max_retries})"
                    )
                    logger.info(f"Headers: {headers}")

                    async with session.request(
                        method=method,
                        url=url,
                        headers=headers,
                        data=data,
                        json=json_data,
                        params=query_params,
                        allow_redirects=True,
                    ) as response:

                        content = await response.text()
                        logger.info(f"Response status: {response.status}")

                        # 检查响应状态
                        if response.status == 200:
                            result = {
                                "status_code": response.status,
                                "headers": dict(response.headers),
                                "content": content,
                                "json": None,
                                "url": str(response.url),
                                "cookies": {
                                    name: cookie.value
                                    for name, cookie in response.cookies.items()
                                },
                            }

                            # 尝试解析JSON
                            try:
                                if response.content_type.startswith("application/json"):
                                    result["json"] = await response.json()
                            except Exception as e:
                                logger.info(f"Failed to parse JSON: {e}")

                            self.set_output("response", result)
                            logger.info(
                                f"Async HTTP request successful: {response.status}"
                            )
                            return result

                        elif self._should_retry(response.status, attempt, max_retries):
                            logger.warning(
                                f"Async HTTP request failed with status {response.status}, retrying..."
                            )
                            await asyncio.sleep(retry_delay)
                            continue
                        else:
                            error_msg = f"HTTP {response.status}: {content[:200]}"
                            logger.error(error_msg)
                            raise Exception(error_msg)

                except asyncio.TimeoutError as e:
                    logger.error(
                        f"Async request timeout on attempt {attempt + 1}: {str(e)}"
                    )
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        raise

                except Exception as e:
                    logger.error(
                        f"Async HTTP request attempt {attempt + 1} failed: {str(e)}"
                    )
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        raise

            raise Exception(f"Async HTTP request failed after {max_retries} attempts")

        finally:
            await session.close()
            if self.connector:
                await self.connector.close()

    def run(self):
        """同步接口，运行异步请求"""
        # 开始前检查取消状态
        self.check_cancellation()

        # 运行异步任务
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        result = loop.run_until_complete(self.run_async())

        # 异步任务完成后检查取消状态
        self.check_cancellation()

        return result


class TableScrapingHelper:
    """表格数据爬取辅助类"""

    @staticmethod
    def generate_page_requests(
        base_url: str,
        base_params: Dict,
        total_pages: int,
        headers: Optional[Dict] = None,
        method: str = "POST",
    ) -> List[Dict]:
        """生成分页请求参数列表"""
        requests_list = []

        for page in range(total_pages):
            # 复制基础参数
            page_params = base_params.copy()
            page_params["page"] = page

            request_config = {
                "url": base_url,
                "method": method,
                "headers": headers or {},
                "json": page_params,
                "delay": random.uniform(
                    settings.SCRAPING_MIN_DELAY, settings.SCRAPING_MAX_DELAY
                ),  # 随机延迟
            }

            requests_list.append(request_config)

        return requests_list

    @staticmethod
    def extract_table_data(response_data: Dict, data_path: str = "data") -> List[Dict]:
        """从响应中提取表格数据"""
        try:
            if response_data.get("status_code") != 200:
                return []

            json_data = response_data.get("json")
            if not json_data:
                return []

            # 根据数据路径提取数据
            data = json_data
            for key in data_path.split("."):
                if isinstance(data, dict) and key in data:
                    data = data[key]
                else:
                    return []

            return data if isinstance(data, list) else []

        except Exception as e:
            logger.error(f"Failed to extract table data: {e}")
            return []

    @staticmethod
    def validate_concurrent_safety(
        total_requests: int,
        concurrent_limit: Optional[int] = None,
        min_delay: Optional[float] = None,
    ) -> Dict[str, Any]:
        """验证并发请求的安全性"""
        # 使用配置中的默认值
        if concurrent_limit is None:
            concurrent_limit = settings.SCRAPING_MAX_CONCURRENT
        if min_delay is None:
            min_delay = settings.SCRAPING_MIN_DELAY

        recommendations = {
            "safe": True,
            "recommended_concurrent": concurrent_limit,
            "recommended_delay": min_delay,
            "warnings": [],
        }

        # 检查并发数是否过高
        max_safe_concurrent = settings.SCRAPING_MAX_CONCURRENT * 2  # 允许的最大并发数
        if concurrent_limit > max_safe_concurrent:
            recommendations["warnings"].append("并发数过高可能导致IP被封")
            recommendations["recommended_concurrent"] = settings.SCRAPING_MAX_CONCURRENT
            recommendations["safe"] = False

        # 检查总请求数
        if total_requests > 100:
            recommendations["warnings"].append("总请求数较大，建议分批处理")
            recommendations["recommended_delay"] = max(
                min_delay, settings.SCRAPING_RETRY_DELAY
            )

        # 检查延迟时间
        min_safe_delay = settings.SCRAPING_MIN_DELAY * 0.5  # 最小安全延迟
        if min_delay < min_safe_delay:
            recommendations["warnings"].append("请求间隔过短，建议增加延迟")
            recommendations["recommended_delay"] = settings.SCRAPING_MIN_DELAY
            recommendations["safe"] = False

        return recommendations


class ModashSearchStep(Step):
    """Modash搜索步骤"""

    def run(self):
        """执行Modash搜索"""
        # 开始前检查取消状态
        self.check_cancellation()

        db = next(get_db())
        task_id = self.get_input("task_id")
        crawler_task.update_status(
            db,
            task_id=task_id,
            status=CrawlerTaskStatusEnum.RUNNING,
            log_msg="任务开始执行",
            append_log=False,
        )

        # 获取参数前再次检查取消状态
        self.check_cancellation()

        cookie = self.get_input("validated_params")["cookies"]
        platform = PlatformEnum[self.get_input("validated_params")["platform"]]
        filters = self.get_input("validated_params")["filters"]
        source = self.get_input("validated_params")["source"]
        page_num = settings.MODASH_FIRST_PAGE_NUM
        filters["page"] = page_num
        platform_urls = {
            PlatformEnum.INSTAGRAM: "https://marketer.modash.io/api/discovery/search/instagram",
            PlatformEnum.TIKTOK: "https://marketer.modash.io/api/discovery/search/tiktok",
            PlatformEnum.YOUTUBE: "https://marketer.modash.io/api/discovery/search/youtube",
        }

        url = platform_urls.get(platform)
        if not url:
            raise ValueError(f"Unsupported platform: {platform}")

        # 设置HTTP请求参数
        self.set_output(
            "http_request_params",
            {
                "url": url,
                "method": "POST",
                "headers": {"cookie": cookie},
                "json": filters,
                "max_retries": AntiDetectionConfig.get_max_retries(),
                "retry_delay": AntiDetectionConfig.get_retry_delay(),
            },
        )

        # 执行HTTP请求前检查取消状态
        self.check_cancellation()

        # 执行HTTP请求 - 使用异步版本
        async_atd_http_step = AsyncAntiDetectHttpRequestStep(self.shared_data)
        # 注入取消任务集合引用
        async_atd_http_step.set_cancelled_tasks_ref(self._cancelled_tasks)
        response = async_atd_http_step.run()

        # 请求完成后检查取消状态
        self.check_cancellation()

        # 检查响应
        if not response or response.get("status_code") != 200:
            raise Exception("Failed to get valid response from Modash API")

        json_data = response.get("json", {})
        pages = json_data.get("pages", 0)

        if pages == 0:
            raise Exception("No data found on Modash")
        if (
            json_data.get("isAuthenticated") is False
            or json_data.get("loggedIn") is False
        ):
            raise Exception("cookie expired")

        # 如果只有一页，直接返回结果
        if pages == 1:
            # TODO looklikes不存在 但是响应为200
            data = json_data.get("lookalikes", [])
            if not data:
                raise Exception("No data found on Modash")
            self.set_output("kol_infos", self._gen_kol_basic_infos(data))
            return

        log_msg = f"成功从{source}的第一页获取总页数: {pages}，开始批量爬取"
        logger.info(log_msg)
        crawler_task.update_status(
            db, task_id=task_id, status=CrawlerTaskStatusEnum.RUNNING, log_msg=log_msg
        )

        # 开始批量处理前检查取消状态
        self.check_cancellation()

        all_data = []

        # 生成多页请求
        requests_list = TableScrapingHelper.generate_page_requests(
            base_url=url,
            base_params=filters,
            total_pages=pages,
            headers={"cookie": cookie},
            method="POST",
        )
        # debug
        # requests_list = requests_list[:2]

        # 执行并发请求前检查取消状态
        self.check_cancellation()
        self.set_output("requests_list", requests_list)
        # 执行并发请求
        async_step = AsyncHttpRequestStep(self.shared_data)
        # 注入取消任务集合引用
        async_step.set_cancelled_tasks_ref(self._cancelled_tasks)

        all_responses = async_step.run()

        # 并发请求完成后检查取消状态
        self.check_cancellation()

        # 合并所有页面的数据
        for response in all_responses:
            # 在处理每个响应前检查取消状态
            self.check_cancellation()

            if response.get("status_code") == 200:
                page_data = TableScrapingHelper.extract_table_data(
                    response, "lookalikes"
                )
                all_data.extend(page_data)
            else:
                logger.warning(
                    f"Page request failed: {response.get('error', 'Unknown error')}"
                )

        # 数据处理完成后最终检查取消状态
        self.check_cancellation()

        log_msg = f"成功爬取【{source}】{pages}页数据，数据总条数：{len(all_data)}"
        logger.info(log_msg)
        crawler_task.update_status(
            db, task_id=task_id, status=CrawlerTaskStatusEnum.RUNNING, log_msg=log_msg
        )
        self.set_output("kol_infos", self._gen_kol_basic_infos(all_data))

    def _gen_kol_basic_infos(self, all_data):
        """生成基础KOL信息"""
        kol_infos = []
        for res in all_data:
            kol_info = {}
            kol_info["project_code"] = self.get_input("validated_params")[
                "project_code"
            ]
            kol_info["platform"] = self.get_input("validated_params")["platform"]
            kol_info["social_url"] = res["profileData"]["profile"]["url"]
            kol_info["social_id"] = res["profileData"]["profile"]["username"]
            kol_info["nick_name"] = res["profileData"]["profile"]["fullname"]
            kol_info["followers_count"] = res["profileData"]["profile"]["followers"]
            kol_info["picture"] = res["profileData"]["profile"]["picture"]
            kol_info["email"] = self._get_email(res)
            kol_info["engagement_rate"] = res["profileData"]["profile"][
                "engagementRate"
            ]
            kol_info["user_id"] = res["profileData"]["userId"]
            kol_infos.append(kol_info)
        return kol_infos

    def _get_email(self, res):

        try:
            email = res.get("emails", [])[0]
            return email_format(email)
        except Exception as e:
            logger.error(f"Failed to get email: {e}")
            return ""
