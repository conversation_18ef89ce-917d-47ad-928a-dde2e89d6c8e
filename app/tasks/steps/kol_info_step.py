import asyncio
import decimal
import json
import logging
import os
import statistics
import time
from decimal import Decimal
from typing import Any, Dict, List, Tuple

from app.api.deps import get_async_db, get_db
from app.config import settings
from app.crud.crawler_task import crawler_task
from app.models.enums import CrawlerTaskStatusEnum
from app.services.instagram_service import InstagramAPIClient
from app.services.llm_service import email_and_keywords_from_bio
from app.services.tiktok_crawler_service import TikTokAP<PERSON>lient
from app.tasks.step import Step
from app.tasks.steps.http_request_step import AsyncHttpRequestStep
from app.utils.email_extractor import BioEmailExtractor
from app.utils.tools import calculate_tier, compute_view_mean_and_median_k

logger = logging.getLogger(__name__)


class KOLInfoStep(Step):
    """KOL信息获取步骤"""

    def __init__(self, shared_data):
        super().__init__(shared_data)

    def run(self):
        """同步接口，运行异步任务"""
        # 运行异步任务

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        result = loop.run_until_complete(self.run_async())
        return result

    async def run_async(self):
        crawler_task_id = self.get_input("crawler_task_id")
        kol_infos = self.get_input("kol_infos")
        db = next(get_db())
        if not kol_infos:
            log_msg = "没有KOL信息需要处理"
            logger.info(log_msg)
            crawler_task.update_status(
                db,
                task_id=crawler_task_id,
                status=CrawlerTaskStatusEnum.COMPLETED,
                log_msg=log_msg,
            )
            return

        project_code = self.get_input("validated_params")["project_code"]
        source = self.get_input("validated_params")["source"]
        platform = self.get_input("validated_params")["platform"]
        hashtags_list, captions_list, topics_list = self.format_filters()

        # 爬虫任务日志 - 异步化
        succ_cnt = 0
        fail_cnt = 0
        all_cnt = len(kol_infos)
        log_msg = f"开始获取KOL详细信息..."

        # 异步更新任务状态
        await self._async_update_crawler_status(
            crawler_task_id, CrawlerTaskStatusEnum.RUNNING, log_msg
        )
        logger.info(log_msg)

        # 创建速率限制信号量 (TikHub QPS: 10/秒)
        rate_limit_semaphore = asyncio.Semaphore(8)  # 稍微保守一点，使用8而不是10

        # debug
        # valid_kol_infos = [kol_info for kol_info in kol_infos if kol_info.get("social_id") == "wine_flameofc"]

        # 分批处理KOL详细信息获取和邮箱提取，统一遵守速率限制
        if kol_infos:
            logger.info(
                f"开始分批获取 {len(kol_infos)} 个KOL的详细信息和邮箱（遵守统一QPS限制）..."
            )

            # 统一处理：详细信息获取 + 邮箱提取
            processed_results = await self._fetch_kol_details_and_emails(kol_infos)

            # 更新统计和KOL信息
            for kol_info, (user_info, videos, succ_tag, email) in zip(
                kol_infos, processed_results
            ):
                succ_cnt += 1 if succ_tag else 0
                fail_cnt += 1 if not succ_tag else 0
                mean_views_k, median_views_k = compute_view_mean_and_median_k(videos)
                followers_count = user_info.get("followers_count", 0)
                kol_info.update(
                    {
                        "email": email,
                        "project_code": project_code,
                        "source": source,
                        "platform": platform,
                        "hashtags": self.merge_hashtags(hashtags_list, videos),
                        "captions": captions_list,
                        "topics": topics_list,
                        "bio": user_info.get("bio", ""),
                        "followers_count": followers_count,
                        "likes_count": user_info.get("likes_count", 0),
                        "tier": calculate_tier(followers_count),
                        "mean_views_k": mean_views_k,
                        "median_views_k": median_views_k,
                    }
                )

            logger.info(
                f"分批获取KOL详细信息和邮箱完成: 成功 {succ_cnt} 个，失败 {fail_cnt} 个"
            )

        # 异步保存到数据库
        await self.async_save_to_db(kol_infos)

        # 异步更新最终状态
        await self._async_update_final_crawler_status(
            crawler_task_id, succ_cnt, fail_cnt, all_cnt
        )

    def merge_hashtags(self, filter_hashtags, videos):
        video_hashtags = set()
        for video in videos:
            if video.get("hashtags") is not None:
                video_hashtags.update(video.get("hashtags"))
        # 合并hashtags（去重）
        merged_hashtags = list(set(filter_hashtags).union(video_hashtags))
        return merged_hashtags

    def update_crawler_status(self, crawler_task_id, succ_cnt, fail_cnt, all_cnt):
        """
        更新爬虫任务状态（适用于1000条以内的数据量级）
        采用双阈值判断：同时考虑失败比例和绝对失败数量
        """
        total_tasks = succ_cnt + fail_cnt
        db = next(get_db())
        # 数据校验
        if total_tasks != all_cnt:
            log_msg = f"统计异常: 成功({succ_cnt}) + 失败({fail_cnt}) ≠ 总数({all_cnt})"
            status = CrawlerTaskStatusEnum.FAILED
        else:
            # 空任务处理
            if all_cnt == 0:
                log_msg = "⚠️ 获取完成: 总爬取数为0"
                status = CrawlerTaskStatusEnum.COMPLETED  # 或ERROR，根据业务需求调整
                crawler_task.update_status(
                    db, task_id=crawler_task_id, status=status, log_msg=log_msg
                )
                logger.info(log_msg)
                return

            # 计算失败率和绝对失败数
            failure_rate = fail_cnt / all_cnt
            absolute_threshold = 50  # 绝对失败数阈值（≥50条视为失败）
            rate_threshold = 0.5  # 失败率阈值（≥50%视为失败）

            # 任务状态判断（同时满足两个阈值才判定为失败）
            if fail_cnt == 0:
                log_msg = f"获取完成: {succ_cnt}/{all_cnt}，全部成功"
                status = CrawlerTaskStatusEnum.COMPLETED
            elif fail_cnt >= absolute_threshold and failure_rate >= rate_threshold:
                log_msg = f"获取失败: {fail_cnt}/{all_cnt} 失败（绝对失败数≥{absolute_threshold}且失败率≥{rate_threshold:.0%}）"
                status = CrawlerTaskStatusEnum.FAILED
            else:
                log_msg = f"获取完成: {succ_cnt}/{all_cnt} 成功，失败: {fail_cnt}/{all_cnt}（失败率{failure_rate:.2%}）"
                status = CrawlerTaskStatusEnum.COMPLETED  # 失败数未达阈值，视为成功

        # 更新任务状态
        crawler_task.update_status(
            db, task_id=crawler_task_id, status=status, log_msg=log_msg
        )
        logger.info(log_msg)

    def format_filters(self) -> Tuple[list, list, list]:
        """
        任务：解析filters（平台筛选条件），生成hashtags, captions, topics数组
        filter示例： /Users/<USER>/prjs/work/kol/script/filter.txt
        对应关系：
        hashtags ： filters.influencer.textTags
        captions ： filters.influencer.keywords
        topics ： filters.influencer.relevance.hashtags

        细节：
        1 使用合适的配置化映射文件，提高可维护性和容错性

        2 注意这个配置文件和解析规则尽可能简单，保证后期维护容易

        Returns:
            Tuple[list, list, list]: hashtags, captions, topics
        """
        filters = self.get_input("validated_params")["filters"]

        try:
            # 加载配置文件
            config = self._load_filter_config()
            mapping = config.get("mapping", {})
            rules = config.get("extraction_rules", {})

            # 初始化结果
            hashtags = []
            captions = []
            topics = []

            # 提取hashtags
            if "hashtags" in mapping:
                hashtags = self._extract_field_data(
                    filters, mapping["hashtags"], rules.get("hashtags", {})
                )

            # 提取captions
            if "captions" in mapping:
                captions = self._extract_field_data(
                    filters, mapping["captions"], rules.get("captions", {})
                )

            # 提取topics
            if "topics" in mapping:
                topics = self._extract_field_data(
                    filters, mapping["topics"], rules.get("topics", {})
                )

            logger.info(
                f"解析filters结果 - hashtags: {len(hashtags)}, captions: {len(captions)}, topics: {len(topics)}"
            )

            return hashtags, captions, topics

        except Exception as e:
            logger.error(f"解析filters失败: {str(e)}")
            return [], [], []

    def _load_filter_config(self) -> Dict[str, Any]:
        """加载filter映射配置文件"""
        try:
            config_path = os.path.join(
                os.path.dirname(__file__), "../../config/filter_mapping.json"
            )
            with open(config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载filter配置文件失败: {str(e)}")
            # 返回默认配置
            return {
                "mapping": {
                    "hashtags": "filters.influencer.textTags",
                    "captions": "filters.influencer.keywords",
                    "topics": "filters.influencer.relevance.hashtags",
                },
                "extraction_rules": {
                    "hashtags": {
                        "type": "array",
                        "filter_field": "type",
                        "filter_value": "hashtag",
                        "value_field": "value",
                        "clean_prefix": "#",
                    },
                    "captions": {"type": "string"},
                    "topics": {"type": "array"},
                },
            }

    def _extract_field_data(
        self, filters: Dict[str, Any], path: str, rule: Dict[str, Any]
    ) -> List[str]:
        """根据路径和规则提取字段数据"""
        try:
            # 根据路径获取数据
            data = self._get_nested_value(filters, path)
            if data is None:
                return []

            result = []

            if rule.get("type") == "array":
                if not isinstance(data, list):
                    return []

                for item in data:
                    # 如果item是字符串，直接添加
                    if isinstance(item, str):
                        result.append(item.strip())
                        continue

                    # 如果item是字典，按规则处理
                    if isinstance(item, dict):
                        # 检查过滤条件
                        if "filter_field" in rule and "filter_value" in rule:
                            if item.get(rule["filter_field"]) != rule["filter_value"]:
                                continue

                        # 提取值
                        value = item.get(rule.get("value_field", "value"), "")
                        if value:
                            # 清理前缀
                            if "clean_prefix" in rule:
                                value = str(value).lstrip(rule["clean_prefix"])
                            result.append(str(value).strip())

            elif rule.get("type") == "string":
                if isinstance(data, str) and data.strip():
                    result.append(data.strip())

            return result

        except Exception as e:
            logger.warning(f"提取字段数据失败 {path}: {str(e)}")
            return []

    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """根据点分隔的路径获取嵌套字典中的值"""
        try:
            keys = path.split(".")
            current = data

            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None

            return current

        except Exception:
            return None

    async def async_save_to_db(self, kol_infos: List[Dict[str, Any]]) -> None:
        """
        异步批量写到kols表中，使用高效的批量操作处理大量数据(50W-100W条)
        采用PostgreSQL的ON CONFLICT DO UPDATE策略实现批量upsert
        """
        if not kol_infos:
            logger.warning("⚠️ 没有KOL数据需要保存")
            return

        from app.crud.base import CRUDBase
        from app.models.kol import KOL

        # 创建CRUD实例
        kol_crud = CRUDBase(KOL)

        # 准备数据
        processed_kol_infos = []
        for kol_info in kol_infos:
            processed_data = self._prepare_kol_data_for_db(kol_info)
            if processed_data:
                processed_kol_infos.append(processed_data)

        if not processed_kol_infos:
            logger.warning("⚠️ 没有有效的KOL数据需要保存")
            return

        try:
            # 使用异步数据库会话
            async for db in get_async_db():
                try:
                    # 使用 async_batch_upsert 方法
                    conflict_columns = [
                        "social_id",
                        "project_code",
                        "platform",
                    ]  # 复合唯一约束

                    upserted_records = await kol_crud.async_batch_upsert(
                        db=db,
                        objs_in=processed_kol_infos,
                        conflict_columns=conflict_columns,
                        batch_size=1000,
                    )

                    logger.info(f"💾 异步KOL数据批量保存完成:")
                    logger.info(f"- 总计处理: {len(kol_infos)} 条")
                    logger.info(f"- 成功处理: {len(upserted_records)} 条")

                    break  # 成功后退出循环

                except Exception as e:
                    logger.error(f"❌ 异步批量保存KOL数据失败: {str(e)}")
                    raise

        except Exception as e:
            logger.error(f"❌ 异步保存KOL数据失败: {str(e)}")
            raise

    def _prepare_kol_data_for_db(self, kol_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        为异步保存准备KOL数据，复用现有的数据处理逻辑
        """
        return self._process_kol_data(kol_info)

    def _batch_upsert_kols(
        self, db, processed_batch: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """
        使用PostgreSQL的ON CONFLICT DO UPDATE执行批量upsert操作

        Args:
            db: 数据库会话
            processed_batch: 预处理后的批次数据

        Returns:
            Dict: 包含插入和更新数量的统计信息
        """
        from sqlalchemy import text
        from sqlalchemy.dialects.postgresql import insert

        from app.models.kol import KOL

        # 构建upsert语句
        stmt = insert(KOL.__table__).values(processed_batch)

        # 定义冲突时的更新字段（排除主键和唯一约束字段）
        # 注意：邮箱获取相关字段只在为空时才更新，避免覆盖已处理的数据
        update_dict = {
            # 基础信息字段 - 总是更新
            "nick_name": stmt.excluded.nick_name,
            "followers_count": stmt.excluded.followers_count,
            "likes_count": stmt.excluded.likes_count,
            "source": stmt.excluded.source,
            "engagement_rate": stmt.excluded.engagement_rate,
            "mean_views_k": stmt.excluded.mean_views_k,
            "median_views_k": stmt.excluded.median_views_k,
            "tier": stmt.excluded.tier,
            "hashtags": stmt.excluded.hashtags,
            "captions": stmt.excluded.captions,
            "topics": stmt.excluded.topics,
            "crawler_task_id": stmt.excluded.crawler_task_id,
            "note": stmt.excluded.note,
            # 邮箱字段 - 只在为空或空字符串时更新
            "email": text(
                "CASE WHEN kols.email IS NULL OR kols.email = '' THEN EXCLUDED.email ELSE kols.email END"
            ),
            # bio字段 - 只在为空或空字符串时更新
            "bio": text(
                "CASE WHEN kols.bio IS NULL OR kols.bio = '' THEN EXCLUDED.bio ELSE kols.bio END"
            ),
            # 邮箱获取流程相关字段 - 只在为空或空字符串时更新，避免覆盖已处理的数据
            "bio_parsed_at": text(
                "COALESCE(kols.bio_parsed_at, EXCLUDED.bio_parsed_at)"
            ),
            "bio_extracted_email": text(
                "CASE WHEN kols.bio_extracted_email IS NULL OR kols.bio_extracted_email = '' THEN EXCLUDED.bio_extracted_email ELSE kols.bio_extracted_email END"
            ),
            "ai_score": text(
                "COALESCE(kols.ai_score, EXCLUDED.ai_score)"
            ),  # 数值类型，只检查NULL
            "ai_matched": text(
                "COALESCE(kols.ai_matched, EXCLUDED.ai_matched)"
            ),  # 布尔类型，只检查NULL
            "ai_scored_at": text(
                "COALESCE(kols.ai_scored_at, EXCLUDED.ai_scored_at)"
            ),  # 日期时间类型，只检查NULL
            "nano_email_fetched_at": text(
                "COALESCE(kols.nano_email_fetched_at, EXCLUDED.nano_email_fetched_at)"
            ),  # 日期时间类型，只检查NULL
            "nano_extracted_email": text(
                "CASE WHEN kols.nano_extracted_email IS NULL OR kols.nano_extracted_email = '' THEN EXCLUDED.nano_extracted_email ELSE kols.nano_extracted_email END"
            ),
            "email_fetch_status": text(
                "CASE WHEN kols.email_fetch_status IS NULL OR kols.email_fetch_status = '' THEN EXCLUDED.email_fetch_status ELSE kols.email_fetch_status END"
            ),
            # 时间戳 - 总是更新
            "updated_at": text("CURRENT_TIMESTAMP"),
        }

        # 使用复合唯一约束进行冲突检测 (social_id, project_code, platform)
        upsert_stmt = stmt.on_conflict_do_update(
            constraint="uk_kols_social_id_project_platform", set_=update_dict
        )

        # 添加返回子句来统计插入和更新的数量
        upsert_stmt = upsert_stmt.returning(
            text("CASE WHEN xmax = 0 THEN 'INSERT' ELSE 'UPDATE' END as operation")
        )

        # 执行upsert操作
        result = db.execute(upsert_stmt)
        operations = result.fetchall()

        # 统计结果
        inserted = sum(1 for op in operations if op[0] == "INSERT")
        updated = sum(1 for op in operations if op[0] == "UPDATE")

        return {"inserted": inserted, "updated": updated}

    def _process_kol_data(self, kol_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理和验证单条KOL数据

        Args:
            kol_info: 原始KOL数据

        Returns:
            Dict: 处理后的数据，如果验证失败返回None
        """
        from app.models.enums import KOLTierEnum, PlatformEnum

        # 必填字段验证
        required_fields = ["social_id", "nick_name", "project_code", "platform"]
        for field in required_fields:
            if not kol_info.get(field):
                raise ValueError(f"缺少必填字段: {field}")

        # 平台枚举验证
        try:
            platform_enum = PlatformEnum(kol_info["platform"].upper())
        except ValueError:
            raise ValueError(f"无效的平台类型: {kol_info['platform']}")

        # KOL分级枚举验证
        tier_enum = None
        if kol_info.get("tier"):
            try:
                tier_enum = KOLTierEnum(kol_info["tier"].upper())
            except ValueError:
                logger.warning(f"⚠️ 无效的KOL分级: {kol_info['tier']}, 将设为None")

        # 构建处理后的数据
        processed_data = {
            # 基础信息字段
            "platform": platform_enum,
            "social_id": kol_info["social_id"],
            "nick_name": kol_info["nick_name"],
            "project_code": kol_info["project_code"],
            "email": self._clean_email_value(kol_info.get("email", "")),  # 清理邮箱值
            "bio": kol_info.get("bio", ""),
            "followers_count": self._safe_int(kol_info.get("followers_count")),
            "likes_count": self._safe_int(kol_info.get("likes_count")),
            "source": kol_info.get("source"),
            "engagement_rate": self._safe_decimal(kol_info.get("engagement_rate")),
            "mean_views_k": self._safe_decimal(kol_info.get("mean_views_k")),
            "median_views_k": self._safe_decimal(kol_info.get("median_views_k")),
            "tier": tier_enum,
            "hashtags": kol_info.get("hashtags", []),
            "captions": kol_info.get("captions", []),
            "topics": kol_info.get("topics", []),
            "crawler_task_id": self.get_input("crawler_task_id"),
            "note": kol_info.get("note", ""),
            # 邮箱获取流程相关字段 - 初始化为None，由后续流程处理
            "bio_parsed_at": None,
            "bio_extracted_email": None,
            "ai_score": None,
            "ai_matched": None,
            "ai_scored_at": None,
            "nano_email_fetched_at": None,
            "nano_extracted_email": None,
            "email_fetch_status": "PENDING",  # 初始状态为PENDING
        }

        return processed_data

    def _safe_int(self, value) -> int:
        """安全转换为整数"""
        if value is None:
            return 0
        try:
            return int(value)
        except (ValueError, TypeError):
            return 0

    def _safe_decimal(self, value) -> Decimal:
        """安全转换为Decimal"""
        if value is None:
            return Decimal("0")
        try:
            return Decimal(str(value))
        except (ValueError, TypeError, decimal.InvalidOperation):
            return Decimal("0")

    def _clean_email_value(self, email_value) -> str:
        """清理邮箱值，处理各种无效情况"""
        if email_value is None:
            return ""

        # 转换为字符串并去除首尾空格
        email_str = str(email_value).strip()

        # 检查是否为各种形式的null值
        if email_str.lower() in ["null", "none", "undefined", "n/a", "na"]:
            return ""

        # 检查是否为空字符串
        if not email_str:
            return ""

        # 简单的邮箱格式验证
        if "@" not in email_str or "." not in email_str:
            return ""

        return email_str

    async def async_get_kol_detail_info(self, user_id: str) -> Tuple[Dict, bool]:
        """
        异步通过user_id请求tikhub接口获取单个kol的详细数据

        Args:
            user_id: Instagram用户ID

        Returns:
            Dict: 包含bio, followers_count, tier, likes_count, mean_views_k, median_views_k的字典
        """
        start_time = time.time()

        try:
            # 构建请求URL - 从配置文件获取
            url = settings.TIKHUB_INSTAGRAM_USER_INFO_URL
            params = {"user_id": user_id}

            # 构建请求头
            headers = {
                "accept": "application/json",
                "Authorization": f"Bearer {settings.TIKHUB_TOKEN}",
            }
            self.set_output(
                "http_request_params",
                {
                    "url": url,
                    "method": "GET",
                    "headers": headers,
                    "params": params,
                    "timeout": 30,
                },
            )
            # 使用AsyncHttpRequestStep发送请求
            http_step = AsyncHttpRequestStep(self.shared_data)

            # 执行请求
            response = await http_step.run_async(
                [
                    {
                        "url": url,
                        "method": "GET",
                        "headers": headers,
                        "params": params,
                    }
                ]
            )

            if (
                not response
                or len(response) == 0
                or response[0].get("status_code") != 200
            ):
                logger.error(f"TikHub API请求失败: {response}")
                return self._get_default_kol_info(), False

            # 解析响应数据
            api_data = response[0].get("json", {})
            if not api_data or api_data.get("code") != 200:
                logger.error(f"TikHub API返回错误: {api_data}")
                return self._get_default_kol_info(), False

            data = api_data.get("data", {})

            # 提取基本信息
            bio = data.get("biography_with_entities", {}).get("raw_text", "")
            followers_count = data.get("edge_followed_by", {}).get("count", 0)

            # 计算tier
            tier = calculate_tier(followers_count)

            # 计算Instagram指标
            # metrics = self._calculate_instagram_metrics(data)

            result = {
                "bio": bio,
                "followers_count": followers_count,
                "tier": tier,
                # "likes_count": metrics["total_likes"],
                # "mean_views_k": metrics["average_views_k"],
                # "median_views_k": metrics["median_views_k"]
            }

            elapsed_time = time.time() - start_time
            logger.info(
                f"异步获取KOL详细信息完成 (user_id: {user_id} result:{result}, 耗时: {elapsed_time:.2f}s)"
            )

            return result, True

        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(
                f"异步获取KOL详细信息失败 (user_id: {user_id}, 耗时: {elapsed_time:.2f}s): {str(e)}"
            )
            self.set_output("crawler_task_status", CrawlerTaskStatusEnum.FAILED)
            return self._get_default_kol_info(), False

    def _get_default_kol_info(self) -> dict:
        """返回默认的KOL信息"""
        return {
            "bio": "",
            "followers_count": 0,
            "tier": "NANO",
            "likes_count": 0,
            "mean_views_k": 0,
            "median_views_k": 0,
        }

    def _calculate_instagram_metrics(self, data: Dict) -> Dict[str, Any]:
        """
        计算Instagram用户的关键指标
        集成自quick_cal_ins.py的计算逻辑

        Args:
            data: TikHub API返回的用户数据

        Returns:
            Dict: 包含total_likes, average_views_k, median_views_k的字典
        """
        start_time = time.time()

        try:
            # 提取媒体节点
            media_data = data.get("edge_owner_to_timeline_media", {})
            media_nodes = media_data.get("edges", [])

            if not media_nodes:
                logger.warning("未找到媒体数据")
                return {"total_likes": 0, "average_views_k": 0, "median_views_k": 0}

            # 初始化统计列表
            like_counts = []
            view_counts = []

            # 遍历所有媒体项目
            for edge in media_nodes:
                node = edge.get("node", {})

                # 收集点赞数
                like_count = node.get("edge_liked_by", {}).get("count")
                if like_count is not None and like_count >= 0:
                    like_counts.append(like_count)

                # 收集观看数（仅视频且观看数大于0）
                if node.get("__typename") == "GraphVideo":
                    view_count = node.get("video_view_count")
                    if view_count is not None and view_count > 0:
                        view_counts.append(view_count)

            # 计算统计指标
            total_likes = sum(like_counts)

            # 观看数统计（转换为千次）
            if view_counts:
                avg_views_k = round(statistics.mean(view_counts) / 1000, 2)
                median_views_k = round(statistics.median(view_counts) / 1000, 2)
            else:
                avg_views_k = 0
                median_views_k = 0

            elapsed_time = time.time() - start_time
            logger.info(
                f"Instagram指标计算完成 (媒体数: {len(media_nodes)}, 有效观看数: {len(view_counts)}, 耗时: {elapsed_time:.3f}s)"
            )

            return {
                "total_likes": total_likes,
                "average_views_k": avg_views_k,
                "median_views_k": median_views_k,
            }

        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"Instagram指标计算失败 (耗时: {elapsed_time:.3f}s): {str(e)}")
            return {"total_likes": 0, "average_views_k": 0, "median_views_k": 0}

    async def _async_update_crawler_status(
        self, task_id: str, status: CrawlerTaskStatusEnum, log_msg: str
    ):
        """异步更新爬虫任务状态"""
        try:
            # 在线程池中执行同步数据库操作
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, self._sync_update_crawler_status, task_id, status, log_msg
            )
        except Exception as e:
            logger.error(f"异步更新爬虫状态失败: {str(e)}")

    def _sync_update_crawler_status(
        self, task_id: str, status: CrawlerTaskStatusEnum, log_msg: str
    ):
        """同步更新爬虫任务状态（在线程池中执行）"""
        db = next(get_db())
        try:
            # 转换 task_id 为 int 类型
            task_id_int = int(task_id)
            crawler_task.update_status(
                db, task_id=task_id_int, status=status, log_msg=log_msg
            )
        finally:
            db.close()

    async def _async_update_final_crawler_status(
        self, task_id: str, succ_cnt: int, fail_cnt: int, all_cnt: int
    ):
        """异步更新最终爬虫任务状态"""
        try:
            # 在线程池中执行同步数据库操作
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self._sync_update_final_crawler_status,
                task_id,
                succ_cnt,
                fail_cnt,
                all_cnt,
            )
        except Exception as e:
            logger.error(f"异步更新最终爬虫状态失败: {str(e)}")

    def _sync_update_final_crawler_status(
        self, task_id: str, succ_cnt: int, fail_cnt: int, all_cnt: int
    ):
        """同步更新最终爬虫任务状态（在线程池中执行）"""
        db = next(get_db())
        try:
            # 转换 task_id 为 int 类型
            task_id_int = int(task_id)
            self.update_crawler_status(task_id_int, succ_cnt, fail_cnt, all_cnt)
        finally:
            db.close()

    async def _async_extract_email_from_bio(self, bio: str) -> tuple:
        """异步从bio中提取邮箱"""
        try:
            # 在线程池中执行同步LLM调用
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, email_and_keywords_from_bio, bio)
            return result
        except Exception as e:
            logger.error(f"异步提取邮箱失败: {str(e)}")
            return None, ""

    async def _rate_limited_get_kol_details(
        self, valid_kol_infos: List[Dict], rate_limit_semaphore: asyncio.Semaphore
    ) -> List:
        """
        遵守速率限制的KOL详细信息获取
        使用分批处理策略，确保严格遵守QPS限制

        Args:
            valid_kol_infos: 有效的KOL信息列表
            rate_limit_semaphore: 速率限制信号量

        Returns:
            详细信息结果列表
        """
        batch_size = 8  # 每批8个请求，保守估计
        batch_delay = 1.0  # 每批之间延迟1秒
        results = []

        logger.info(f"开始分批处理 {len(valid_kol_infos)} 个KOL，每批 {batch_size} 个")

        for i in range(0, len(valid_kol_infos), batch_size):
            batch = valid_kol_infos[i : i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(valid_kol_infos) + batch_size - 1) // batch_size

            logger.info(
                f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch)} 个KOL"
            )

            # 处理当前批次
            batch_tasks = []
            for kol_info in batch:

                async def rate_limited_task(kol_info_inner=kol_info):
                    try:
                        user_id = kol_info_inner.get("user_id")
                        return await self.async_get_kol_detail_info(user_id)
                    except Exception as e:
                        logger.error(
                            f"KOL详细信息获取失败 (user_id: {kol_info_inner.get('user_id')}): {str(e)}"
                        )
                        return e

                batch_tasks.append(rate_limited_task())

            # 执行当前批次
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            results.extend(batch_results)

            # 批次间延迟（除了最后一批）
            if i + batch_size < len(valid_kol_infos):
                logger.info(f"批次完成，等待 {batch_delay} 秒...")
                await asyncio.sleep(batch_delay)

        logger.info(f"所有批次处理完成，共获取 {len(results)} 个结果")
        return results

    async def _fetch_kol_details_and_emails(
        self, valid_kol_infos: List[Dict]
    ) -> List[Tuple]:
        """
        统一的速率限制处理：KOL详细信息获取 + 邮箱提取
        将所有外部API调用（TikHub + OpenAI）都纳入统一的速率限制管理

        Args:
            valid_kol_infos: 有效的KOL信息列表
            rate_limit_semaphore: 速率限制信号量
            其他参数: KOL信息更新所需的参数

        Returns:
            处理结果列表: [(detail, succ_tag, email), ...]
        """
        batch_size = 5  # 更保守的批次大小，因为每个KOL需要2个API调用
        batch_delay = 1.2  # 批次间延迟稍长一些
        results = []

        logger.info(
            f"开始统一分批处理 {len(valid_kol_infos)} 个KOL（详细信息+邮箱），每批 {batch_size} 个"
        )

        for i in range(0, len(valid_kol_infos), batch_size):
            batch = valid_kol_infos[i : i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(valid_kol_infos) + batch_size - 1) // batch_size

            logger.info(
                f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch)} 个KOL"
            )

            # 处理当前批次
            batch_tasks = []
            for kol_info in batch:

                async def unified_kol_task(kol_info_inner=kol_info):
                    """统一的KOL处理任务：详细信息 + 邮箱"""
                    try:
                        # 1. 获取KOL详细信息
                        platform = kol_info_inner.get("platform")
                        url = kol_info_inner.get("social_url")
                        user_name = kol_info_inner.get("social_id")
                        if platform == "TIKTOK":
                            async with TikTokAPIClient(url=url) as client:
                                user_info, videos, succ_tag = (
                                    await client.scrape_async()
                                )
                        if platform == "INSTAGRAM":
                            async with InstagramAPIClient(user_name) as client:
                                user_info, videos, succ_tag = (
                                    await client.scrape_async()
                                )

                        # 2. 提取邮箱（OpenAI API）
                        email = kol_info_inner.get("email")
                        bio = user_info.get("bio", "")
                        if email:
                            return (user_info, videos, succ_tag, email)

                        # 尝试手工解析邮箱, 降低模型费用，可能没有邮箱的，或者手工识别出邮箱了的直接返回，
                        extractor = BioEmailExtractor()
                        has_potential, email = extractor.extract(bio)
                        if not has_potential or email:
                            return (user_info, videos, succ_tag, email)
                        try:
                            _, email = await self._async_extract_email_from_bio(bio)
                        except Exception as e:
                            logger.error(f"邮箱提取失败 (url: {url}): {str(e)}")

                        return (user_info, videos, succ_tag, email)

                    except Exception as e:
                        logger.error(
                            f"统一KOL处理失败 (user_id: {kol_info_inner.get('user_id')}): {str(e)}"
                        )
                        return (self._get_default_kol_info(), False, "")

                batch_tasks.append(unified_kol_task())

            # 执行当前批次（每个任务内部包含2个API调用）
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理异常结果
            processed_batch_results = []
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"批次任务异常: {result}")
                    processed_batch_results.append(
                        (self._get_default_kol_info(), False, "")
                    )
                else:
                    processed_batch_results.append(result)

            results.extend(processed_batch_results)

            # 批次间延迟（除了最后一批）
            if i + batch_size < len(valid_kol_infos):
                logger.info(f"批次完成，等待 {batch_delay} 秒...")
                await asyncio.sleep(batch_delay)

        logger.info(f"统一分批处理完成，共处理 {len(results)} 个KOL")
        return results
