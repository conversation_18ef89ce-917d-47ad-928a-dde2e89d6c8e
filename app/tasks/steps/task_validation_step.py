"""
任务参数验证步骤
验证爬虫任务的输入参数
"""

from pydantic import BaseModel

from app.logging_config import get_logger
from app.models.enums import PlatformEnum, SourceEnum

logger = get_logger("task_validation_step")


class ParamsChecker(BaseModel):
    task_name: str
    source: SourceEnum
    platform: PlatformEnum
    project_code: str
    filters: dict
    cookies: str

    class Config:
        str_min_length = 1
        str_strip_whitespace = True
