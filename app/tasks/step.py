from app.logging_config import get_logger
from app.utils.wrapper import log_run_time

logger = get_logger("step")


class TaskCancelledException(Exception):
    """任务被取消异常"""

    def __init__(self, task_id: int, message: str = "Task was cancelled"):
        self.task_id = task_id
        self.message = message
        super().__init__(f"Task {task_id}: {message}")


class Step:

    def __init__(self, shared_data={}) -> None:
        self.shared_data = shared_data
        self._cancelled_tasks = None  # 将在运行时注入

    def run(self):
        raise NotImplementedError("Subclass must implement run method")

    @log_run_time
    def run_with_log(self):
        return self.run()

    def set_output(self, key, value):
        self.shared_data[key] = value

    def get_input(self, key):
        return self.shared_data.get(key)

    def set_cancelled_tasks_ref(self, cancelled_tasks_set):
        """设置取消任务集合的引用"""
        self._cancelled_tasks = cancelled_tasks_set

    def check_cancellation(self):
        """检查任务是否被取消，如果被取消则抛出异常"""
        task_id = self.get_input("task_id")
        if task_id and self._cancelled_tasks and task_id in self._cancelled_tasks:
            logger.info(
                f"Task {task_id} cancellation detected in step {self.__class__.__name__}"
            )
            raise TaskCancelledException(
                task_id, f"Task cancelled during {self.__class__.__name__}"
            )

    def run_with_cancellation_check(self):
        """带取消检查的运行方法"""
        # 执行前检查取消状态
        self.check_cancellation()

        # 执行步骤
        try:
            result = self.run_with_log()

            # 执行后再次检查取消状态
            self.check_cancellation()

            return result
        except TaskCancelledException:
            # 重新抛出取消异常
            raise
        except Exception:
            # 其他异常也要检查取消状态
            self.check_cancellation()
            raise
