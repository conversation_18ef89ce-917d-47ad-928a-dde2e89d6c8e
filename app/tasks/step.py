import threading
from typing import Optional

from app.logging_config import get_logger
from app.utils.wrapper import log_run_time

logger = get_logger("step")


class TaskCancelledException(Exception):
    """任务被取消异常"""

    def __init__(self, task_id: int, message: str = "Task was cancelled"):
        self.task_id = task_id
        self.message = message
        super().__init__(f"Task {task_id}: {message}")


class CancellationSignal:
    """任务取消信号量"""

    def __init__(self):
        self._event = threading.Event()
        self._cancelled = False

    def cancel(self):
        """发送取消信号"""
        self._cancelled = True
        self._event.set()

    def is_cancelled(self) -> bool:
        """检查是否已取消"""
        return self._cancelled

    def wait_for_cancellation(self, timeout: Optional[float] = None) -> bool:
        """等待取消信号，返回是否收到取消信号"""
        return self._event.wait(timeout)


class Step:
    """任务步骤基类 - 简化版本，移除埋点检查机制"""

    def __init__(self, shared_data={}) -> None:
        self.shared_data = shared_data

    def run(self):
        """执行步骤的主要逻辑，子类必须实现此方法"""
        raise NotImplementedError("Subclass must implement run method")

    @log_run_time
    def run_with_log(self):
        """带日志记录的运行方法"""
        return self.run()

    def set_output(self, key, value):
        """设置输出数据到共享数据中"""
        self.shared_data[key] = value

    def get_input(self, key):
        """从共享数据中获取输入数据"""
        return self.shared_data.get(key)
