import os
import sys

from loguru import logger

from app.config import settings


def setup_logging():
    """配置日志系统，使用Loguru替代传统的logging"""
    # 确保日志目录存在
    os.makedirs(settings.LOG_FILE_PATH, exist_ok=True)

    # 清除默认的处理器
    logger.remove()

    # 获取正确的日志级别
    log_level = settings.LOG_LEVEL.upper()

    # 添加控制台处理器，带有颜色
    logger.add(
        sys.stdout,
        colorize=True,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=log_level,
    )

    # 添加文件处理器，使用轮转配置
    logger.add(
        settings.log_file,
        rotation=getattr(settings, "LOG_MAX_BYTES", 10 * 1024 * 1024),  # 默认10MB
        retention=getattr(settings, "LOG_BACKUP_COUNT", 5),  # 默认保留5个备份
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}",
        level=log_level,
        encoding="utf8",
    )

    # 配置请求日志文件（专门记录API请求）
    api_log_file = os.path.join(settings.LOG_FILE_PATH, "api_requests.log")
    logger.add(
        api_log_file,
        rotation=getattr(settings, "LOG_MAX_BYTES", 10 * 1024 * 1024),  # 默认10MB
        retention=getattr(settings, "LOG_BACKUP_COUNT", 5),  # 默认保留5个备份
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | REQUEST | {message}",
        filter=lambda record: "api_request" in record["extra"],
        level=log_level,
        encoding="utf8",
    )

    # 配置异常日志文件
    error_log_file = os.path.join(settings.LOG_FILE_PATH, "errors.log")
    logger.add(
        error_log_file,
        rotation=getattr(settings, "LOG_MAX_BYTES", 10 * 1024 * 1024),  # 默认10MB
        retention=getattr(settings, "LOG_BACKUP_COUNT", 5),  # 默认保留5个备份
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}\n{exception}",
        filter=lambda record: record["level"].name == "ERROR"
        or record["level"].name == "CRITICAL",
        level="ERROR",
        encoding="utf8",
        backtrace=True,
        diagnose=True,
    )

    # 记录配置完成日志
    logger.info(
        f"日志系统初始化完成: 日志级别={log_level}, 主日志文件={settings.log_file}"
    )
    logger.info(f"API请求日志文件: {api_log_file}")
    logger.info(f"错误日志文件: {error_log_file}")

    return logger


def get_logger(name):
    """
    获取带有模块名的logger

    Args:
        name: 模块名称

    Returns:
        配置好的logger实例
    """
    return logger.bind(name=name)


def get_request_logger():
    """
    获取专门用于记录API请求的logger

    Returns:
        配置好的logger实例，带有api_request标记
    """
    return logger.bind(api_request=True)


def get_app_logger():
    """
    获取专门用于记录应用级别的日志的logger

    Returns:
        配置好的logger实例，带有app标记
    """
    return logger.bind(app=True)


# 当直接运行此模块时，执行测试
if __name__ == "__main__":
    setup_logging()

    print("=" * 50)
    print("测试Loguru日志配置")
    print(f"日志文件路径: {settings.log_file}")
    print("=" * 50)

    # 测试各种日志级别
    logger.debug("这是一条DEBUG日志信息")
    logger.info("这是一条INFO日志信息")
    logger.warning("这是一条WARNING日志信息")
    logger.error("这是一条ERROR日志信息")
    logger.critical("这是一条CRITICAL日志信息")

    # 测试模块日志记录器
    app_logger = get_logger("app.test")
    app_logger.info("这是来自应用日志记录器的消息")

    # 测试请求日志记录器
    req_logger = get_request_logger()
    req_logger.info("GET /api/v1/users - 200 OK - 45ms")


    # 测试异常记录
    try:
        raise ValueError("这是一个测试异常")
    except Exception as e:
        logger.exception(f"捕获到异常: {str(e)}")

    print("=" * 50)
    print(f"日志测试完成，请查看日志文件: {settings.log_file}")
    print("=" * 50)
