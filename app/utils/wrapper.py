import time

from app.logging_config import get_logger

logger = get_logger("wrapper")


def log_run_time(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(
            f"Function {func.__name__} took {end_time - start_time:.2f} seconds to run"
        )
        return result

    return wrapper
