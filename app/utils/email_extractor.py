import re
from typing import Optional, Tuple


class BioEmailExtractor:
    def __init__(self):
        # 邮箱格式匹配正则
        self.patterns = [
            # 标准格式：<EMAIL>
            re.compile(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"),
            # 带空格：xxx @ xxx.com
            re.compile(r"[a-zA-Z0-9._%+-]+\s*@\s*[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"),
            # at替代@：xxx at xxx.com
            re.compile(
                r"([a-zA-Z0-9._%+-]+)\s+(at|AT)\s+([a-zA-Z0-9.-]+)\.([a-zA-Z]{2,})"
            ),
            # [at]或(at)替代@：xxx[at]xxx.com
            re.compile(
                r"([a-zA-Z0-9._%+-]+)\s*[\(\[]?at[\)\]]?\s*([a-zA-Z0-9.-]+)\.([a-zA-Z]{2,})"
            ),
            # dot替代.：xxx@xxx dot com
            re.compile(
                r"([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+)\s+(dot|DOT)\s+([a-zA-Z]{2,})"
            ),
            # 常见前缀：contact@xxx等
            re.compile(
                r"(contact|mail|email|info|hello|support|service)\s*@\s*[a-zA-Z0-9.-]+"
            ),
        ]

        # 邮箱相关关键词
        self.keywords = {
            "email",
            "mail",
            "contact",
            "info",
            "hello",
            "support",
            "邮箱",
            "联系",
            "邮件",
            "@",
            ".com",
            ".cn",
            ".net",
            ".org",
            "tiktok",
            "instagram",
            "youtube",
            "t.co",
            "instagram.com",
            "youtube.com",
            "tiktok.com",
            "tiktok",
            "insta",
            "yt",
            "youtube",
            "influencer",
            "bio",
            "link",
            "follow",
        }

    def _clean(self, candidate: str) -> str:
        """清理邮箱格式"""
        cleaned = re.sub(r"\s+", "", candidate)
        cleaned = re.sub(r"[\(\[]?at[\)\]]?", "@", cleaned, flags=re.IGNORECASE)
        cleaned = re.sub(r"[\(\[]?dot[\)\]]?", ".", cleaned, flags=re.IGNORECASE)
        cleaned = re.sub(r"[^\w@\.]", "", cleaned)
        return cleaned

    def _valid(self, email: str) -> bool:
        """验证邮箱有效性"""
        if len(email) > 254:
            return False
        return bool(
            re.match(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", email)
        )

    def extract(self, bio: str) -> Tuple[bool, Optional[str]]:
        """
        提取bio中的第一个有效邮箱

        Args:
            bio: KOL的bio文本

        Returns:
            (has_potential, email)
            - has_potential: 是否可能有邮箱
            - email: 第一个有效邮箱，无则为None
        """
        if not bio or len(bio.strip()) < 5:
            return (False, None)

        bio_clean = bio.strip()

        # 检查关键词
        has_potential = any(k in bio_clean.lower() for k in self.keywords)
        if not has_potential:
            return (False, None)

        # 提取并返回第一个有效邮箱
        for p in self.patterns:
            for match in p.findall(bio_clean):
                candidate = "".join(match) if isinstance(match, tuple) else match
                email = self._clean(candidate)
                if self._valid(email):
                    return (True, email.lower())

        # 有潜在但未提取到有效邮箱
        return (True, None)


# 使用示例
if __name__ == "__main__":
    extractor = BioEmailExtractor()

    test_cases = [
        "合作邮箱: <EMAIL> | 商务: <EMAIL>",
        "Email: support[at]test.org",
        "联系我们：电话123456",
        "Hello! Follow me on social media",
    ]

    for bio in test_cases:
        has_potential, email = extractor.extract(bio)
        print(f"Bio: {bio[:30]}...")
        print(f"可能有邮箱: {has_potential}, 第一个邮箱: {email}\n")
