import re

from app import logging_config

logger = logging_config.get_logger("tools")


def compute_view_mean_and_median_k(videos: list):
    """
    计算视频播放量的平均数和中位数
    Args:
        videos: 视频列表，每个视频包含 play_count 和 is_pinned 属性
    Returns:
        tuple: (平均播放量(k), 中位数播放量(k))
    """
    # 过滤掉置顶视频
    non_pinned_videos = [video for video in videos if not video.get("is_pinned", False)]

    # 只取前15个视频
    selected_videos = non_pinned_videos[:15]

    # 如果没有视频，返回0
    if not selected_videos:
        return 0, 0

    # 获取播放量列表
    play_counts = [video["play_count"] for video in selected_videos]

    # 计算平均数
    avg = sum(play_counts) / len(play_counts)

    # 计算中位数
    sorted_counts = sorted(play_counts)
    mid = len(sorted_counts) // 2
    if len(sorted_counts) % 2 == 0:
        median = (sorted_counts[mid - 1] + sorted_counts[mid]) / 2
    else:
        median = sorted_counts[mid]

    # 转换为k单位（除以1000）
    avg_in_k = round(avg / 1000, 2)
    median_in_k = round(median / 1000, 2)

    return avg_in_k, median_in_k


def calculate_tier(followers_count: int) -> str:
    """
    根据粉丝数计算KOL分级

    Args:
        followers_count: 粉丝数

    Returns:
        str: KOL分级 (NANO, MICRO, MID, MACRO, MEGA)
    """
    if followers_count < 1000:
        return "NANO"
    elif followers_count < 10000:
        return "MICRO"
    elif followers_count < 100000:
        return "MID"
    elif followers_count < 1000000:
        return "MACRO"
    else:
        return "MEGA"


def email_format(email):
    try:
        if not email or not isinstance(email, str):
            return ""

        # 邮箱格式验证的正则表达式
        # 基本的邮箱格式：用户名@域名.后缀
        email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"

        # 验证邮箱格式
        if re.match(email_pattern, email.strip()):
            return email.strip()
        else:
            logger.debug(f"Invalid email format: {email}")
            return ""

    except Exception as e:
        logger.debug(f"Error extracting email: {e}")
        return ""
