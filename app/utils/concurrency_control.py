"""
异步并发控制工具
"""

import asyncio
import random
from functools import wraps
from typing import Any, Callable, Dict, Optional

from fake_useragent import UserAgent

from app.config import settings
from app.logging_config import get_logger

logger = get_logger("concurrency_control")

# 全局信号量，用于控制异步接口的并发数
_async_semaphore = asyncio.Semaphore(settings.ASYNC_MAX_CONCURRENCY)


def async_concurrency_limit(func: Callable) -> Callable:
    """
    异步并发控制装饰器

    限制被装饰的异步函数的并发执行数量
    """

    @wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        async with _async_semaphore:
            logger.debug(
                f"开始执行异步函数: {func.__name__}, 当前并发数: {settings.ASYNC_MAX_CONCURRENCY - _async_semaphore._value}"
            )
            try:
                result = await func(*args, **kwargs)
                logger.debug(f"异步函数执行完成: {func.__name__}")
                return result
            except Exception as e:
                logger.error(f"异步函数执行失败: {func.__name__}, 错误: {str(e)}")
                raise

    return wrapper


def get_current_concurrency_info() -> dict:
    """
    获取当前并发控制信息
    """
    return {
        "max_concurrency": settings.ASYNC_MAX_CONCURRENCY,
        "current_running": settings.ASYNC_MAX_CONCURRENCY - _async_semaphore._value,
        "available_slots": _async_semaphore._value,
    }


def update_max_concurrency(new_limit: int) -> bool:
    """
    动态更新最大并发数

    Args:
        new_limit: 新的最大并发数

    Returns:
        bool: 是否更新成功
    """
    global _async_semaphore

    if new_limit <= 0:
        logger.error(f"无效的并发数限制: {new_limit}")
        return False

    try:
        # 创建新的信号量
        old_limit = settings.ASYNC_MAX_CONCURRENCY
        _async_semaphore = asyncio.Semaphore(new_limit)

        # 更新配置
        settings.ASYNC_MAX_CONCURRENCY = new_limit

        logger.info(f"并发数限制已更新: {old_limit} -> {new_limit}")
        return True
    except Exception as e:
        logger.error(f"更新并发数限制失败: {str(e)}")
        return False


class AntiDetectionConfig:
    """反爬虫检测配置类 - 从settings获取配置"""

    @classmethod
    def get_random_user_agent(cls) -> str:
        """获取随机User-Agent"""
        if settings.SCRAPING_USER_AGENT_ROTATION:
            try:
                ua = UserAgent()
                return ua.random
            except Exception:
                pass
        return random.choice(settings.SCRAPING_USER_AGENTS)

    @classmethod
    def get_random_delay(cls) -> float:
        """获取随机延迟时间"""
        return random.uniform(settings.SCRAPING_MIN_DELAY, settings.SCRAPING_MAX_DELAY)

    @classmethod
    def build_headers(
        cls, custom_headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """构建请求头"""
        headers = settings.SCRAPING_COMMON_HEADERS.copy()

        if settings.SCRAPING_USER_AGENT_ROTATION:
            headers["User-Agent"] = cls.get_random_user_agent()

        if custom_headers:
            headers.update(custom_headers)

        return headers

    @classmethod
    def get_max_retries(cls) -> int:
        """获取最大重试次数"""
        return settings.SCRAPING_MAX_RETRIES

    @classmethod
    def get_retry_delay(cls) -> float:
        """获取重试延迟"""
        return settings.SCRAPING_RETRY_DELAY

    @classmethod
    def get_timeout(cls) -> int:
        """获取超时时间"""
        return settings.SCRAPING_TIMEOUT

    @classmethod
    def get_max_concurrent(cls) -> int:
        """获取最大并发数"""
        return settings.SCRAPING_MAX_CONCURRENT

    @classmethod
    def is_anti_detection_enabled(cls) -> bool:
        """检查是否启用反爬虫检测"""
        return settings.SCRAPING_ENABLE_ANTI_DETECTION
